#include "SpeedBrushRenderer.h"
#include <QDebug>
#include <QtMath>
#include <QPolygonF>
#include <QVector2D>

void SpeedBrushRenderer::renderSpeedBrushPath(QPainter* painter, 
                                            const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
                                            const QPen& basePen,
                                            const QBrush& brush,
                                            ToolType toolType)
{
    if (!painter || points.isEmpty()) {
        return;
    }

    // 如果只有一个点，绘制圆点
    if (points.size() == 1) {
        const auto& point = points.first();
        painter->save();
        painter->setPen(Qt::NoPen);
        painter->setBrush(brush);
        painter->drawEllipse(point.position, point.width / 2.0, point.width / 2.0);
        painter->restore();
        return;
    }

    // 优化点数据
    QVector<IncrementalPathBuilder::SpeedBrushPoint> optimizedPoints = optimizePoints(points);
    
    // 创建变宽度描边路径
    QPainterPath strokePath = createVariableWidthStroke(optimizedPoints);
    
    // 使用羽化效果渲染
    renderWithFeathering(painter, strokePath, basePen, brush, toolType);
}

void SpeedBrushRenderer::renderSpeedBrushPath(QPainter* painter, 
                                            const QPainterPath& path,
                                            const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
                                            const QPen& basePen,
                                            const QBrush& brush,
                                            ToolType toolType)
{
    if (!painter) {
        return;
    }

    // 如果提供了预构建的路径，直接使用
    if (!path.isEmpty()) {
        renderWithFeathering(painter, path, basePen, brush, toolType);
        return;
    }

    // 否则回退到点数据渲染
    renderSpeedBrushPath(painter, points, basePen, brush, toolType);
}

QPainterPath SpeedBrushRenderer::createVariableWidthStroke(
    const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points)
{
    QPainterPath strokePath;
    
    if (points.size() < 2) {
        return strokePath;
    }

    // 创建左右边界点
    QVector<QPointF> leftBoundary;
    QVector<QPointF> rightBoundary;
    
    leftBoundary.reserve(points.size());
    rightBoundary.reserve(points.size());

    // 计算每个点的边界
    for (int i = 0; i < points.size(); ++i) {
        const auto& currentPoint = points[i];
        
        QPointF direction;
        
        if (i == 0) {
            // 第一个点：使用到下一个点的方向
            direction = points[i + 1].position - currentPoint.position;
        } else if (i == points.size() - 1) {
            // 最后一个点：使用从上一个点的方向
            direction = currentPoint.position - points[i - 1].position;
        } else {
            // 中间点：使用平均方向
            QPointF prevDir = currentPoint.position - points[i - 1].position;
            QPointF nextDir = points[i + 1].position - currentPoint.position;

            // 手动归一化向量（Qt 6.9中QPointF没有normalized方法）
            auto normalizeVector = [](const QPointF& vec) -> QPointF {
                qreal length = qSqrt(vec.x() * vec.x() + vec.y() * vec.y());
                if (length > 0.0) {
                    return QPointF(vec.x() / length, vec.y() / length);
                }
                return QPointF(0, 0);
            };

            QPointF normalizedPrevDir = normalizeVector(prevDir);
            QPointF normalizedNextDir = normalizeVector(nextDir);
            direction = normalizeVector(normalizedPrevDir + normalizedNextDir);
        }
        
        // 计算垂直向量
        QPointF perpendicular = calculatePerpendicularVector(QPointF(0, 0), direction, currentPoint.width / 2.0);
        
        leftBoundary.append(currentPoint.position + perpendicular);
        rightBoundary.append(currentPoint.position - perpendicular);
    }

    // 构建描边路径
    if (!leftBoundary.isEmpty() && !rightBoundary.isEmpty()) {
        // 添加左边界
        strokePath.moveTo(leftBoundary.first());
        for (int i = 1; i < leftBoundary.size(); ++i) {
            strokePath.lineTo(leftBoundary[i]);
        }
        
        // 添加右边界（反向）
        for (int i = rightBoundary.size() - 1; i >= 0; --i) {
            strokePath.lineTo(rightBoundary[i]);
        }
        
        // 闭合路径
        strokePath.closeSubpath();
    }

    return strokePath;
}

QPainterPath SpeedBrushRenderer::createSegmentStroke(
    const IncrementalPathBuilder::SpeedBrushPoint& start,
    const IncrementalPathBuilder::SpeedBrushPoint& end)
{
    QPainterPath segmentPath;
    
    // 计算方向向量
    QPointF direction = end.position - start.position;
    qreal length = QVector2D(direction).length();
    
    if (length < MIN_SEGMENT_LENGTH) {
        return segmentPath;
    }
    
    // 计算垂直向量
    QPointF startPerp = calculatePerpendicularVector(start.position, end.position, start.width / 2.0);
    QPointF endPerp = calculatePerpendicularVector(start.position, end.position, end.width / 2.0);
    
    // 创建四边形路径
    QPolygonF polygon;
    polygon << (start.position + startPerp)
            << (end.position + endPerp)
            << (end.position - endPerp)
            << (start.position - startPerp);
    
    segmentPath.addPolygon(polygon);
    
    return segmentPath;
}

void SpeedBrushRenderer::renderWithFeathering(QPainter* painter, 
                                            const QPainterPath& strokePath,
                                            const QPen& pen, 
                                            const QBrush& brush,
                                            ToolType toolType)
{
    if (strokePath.isEmpty()) {
        return;
    }

    painter->save();
    
    // 设置画笔为无边框，使用画刷填充
    QPen noPen = pen;
    noPen.setStyle(Qt::NoPen);
    
    // 使用FeatheringRenderer进行羽化渲染
    FeatheringRenderer::drawPathWithFeathering(painter, strokePath, noPen, brush, toolType);
    
    painter->restore();
}

QPointF SpeedBrushRenderer::calculatePerpendicularVector(const QPointF& start, const QPointF& end, qreal width)
{
    QPointF direction = end - start;
    qreal length = QVector2D(direction).length();
    
    if (length < 0.001) {
        // 如果长度太小，返回默认垂直向量
        return QPointF(0, width);
    }
    
    // 标准化方向向量
    direction /= length;
    
    // 计算垂直向量（逆时针旋转90度）
    QPointF perpendicular(-direction.y(), direction.x());
    
    return perpendicular * width;
}

QVector<IncrementalPathBuilder::SpeedBrushPoint> SpeedBrushRenderer::optimizePoints(
    const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points)
{
    if (points.size() <= 2) {
        return points;
    }

    // 检查是否需要简化
    if (needsPathSimplification(points)) {
        return simplifyPoints(points, SIMPLIFICATION_TOLERANCE);
    }

    // 移除重复点和过近的点
    QVector<IncrementalPathBuilder::SpeedBrushPoint> optimized;
    optimized.reserve(points.size());
    
    optimized.append(points.first());
    
    for (int i = 1; i < points.size(); ++i) {
        const auto& current = points[i];
        const auto& last = optimized.last();
        
        // 计算距离
        qreal distance = QVector2D(current.position - last.position).length();
        
        // 如果距离足够大，或者是最后一个点，则添加
        if (distance >= MIN_SEGMENT_LENGTH || i == points.size() - 1) {
            optimized.append(current);
        }
    }
    
    return optimized;
}

bool SpeedBrushRenderer::needsPathSimplification(const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points)
{
    return points.size() > MAX_POINTS_FOR_FULL_QUALITY;
}

QVector<IncrementalPathBuilder::SpeedBrushPoint> SpeedBrushRenderer::simplifyPoints(
    const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points, qreal tolerance)
{
    if (points.size() <= 2) {
        return points;
    }

    QVector<IncrementalPathBuilder::SpeedBrushPoint> simplified;
    simplified.reserve(points.size() / 2); // 预估简化后的大小
    
    // 使用Douglas-Peucker算法的简化版本
    simplified.append(points.first());
    
    for (int i = 1; i < points.size() - 1; ++i) {
        const auto& prev = simplified.last();
        const auto& current = points[i];
        const auto& next = points[i + 1];
        
        // 计算当前点到前后点连线的距离
        QPointF line = next.position - prev.position;
        QPointF toPoint = current.position - prev.position;
        
        qreal lineLength = QVector2D(line).length();
        if (lineLength < 0.001) {
            continue;
        }
        
        // 计算垂直距离
        qreal distance = qAbs(QVector2D::dotProduct(QVector2D(toPoint), QVector2D(-line.y(), line.x()).normalized()));
        
        // 如果距离大于容差，保留该点
        if (distance > tolerance) {
            simplified.append(current);
        }
    }
    
    // 总是保留最后一个点
    simplified.append(points.last());
    
    return simplified;
}