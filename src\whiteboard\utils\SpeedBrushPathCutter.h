#ifndef SPEEDBRUSHPATHCUTTER_H
#define SPEEDBRUSHPATHCUTTER_H

#include <QVector>
#include <QPainterPath>
#include "../data/SpeedBrushData.h"
#include "../optimization/IncrementalPathBuilder.h"
#include "../utils/WidthInterpolator.h"

/**
 * @brief 速度笔锋路径切割器
 * 
 * 负责处理速度笔锋路径的切割操作，确保切割后的路径段保持正确的笔锋效果
 */
class SpeedBrushPathCutter
{
public:
    /**
     * @brief 切割结果结构
     */
    struct CutResult {
        QVector<SpeedBrushData> segments;  // 切割后的路径段
        bool wasModified = false;          // 是否发生了切割
    };
    
    /**
     * @brief 使用橡皮擦路径切割速度笔锋路径
     * @param speedBrushData 原始速度笔锋数据
     * @param eraserPath 橡皮擦路径
     * @param eraserWidth 橡皮擦宽度
     * @return 切割结果
     */
    static CutResult cutSpeedBrushPath(const SpeedBrushData& speedBrushData,
                                     const QPainterPath& eraserPath,
                                     qreal eraserWidth);
    
    /**
     * @brief 检查点是否被橡皮擦路径覆盖
     * @param point 要检查的点
     * @param pointWidth 点的宽度
     * @param eraserPath 橡皮擦路径
     * @param eraserWidth 橡皮擦宽度
     * @return 是否被覆盖
     */
    static bool isPointErased(const QPointF& point, qreal pointWidth,
                            const QPainterPath& eraserPath, qreal eraserWidth);
    
    /**
     * @brief 在指定索引处分割速度笔锋数据
     * @param speedBrushData 原始数据
     * @param splitIndex 分割索引
     * @return 分割后的两个数据段
     */
    static QVector<SpeedBrushData> splitSpeedBrushData(const SpeedBrushData& speedBrushData,
                                                      int splitIndex);
    
    /**
     * @brief 重新计算路径段的速度笔锋效果
     * @param points 路径点
     * @param originalConfig 原始配置
     * @return 重新计算后的速度笔锋数据
     */
    static SpeedBrushData recalculateSpeedBrushData(const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
                                                   const WidthInterpolator::WidthConfig& originalConfig);

private:
    /**
     * @brief 计算两点之间的距离
     */
    static qreal distance(const QPointF& p1, const QPointF& p2);
    
    /**
     * @brief 检查线段是否与橡皮擦路径相交
     */
    static bool segmentIntersectsEraser(const QPointF& start, const QPointF& end,
                                      qreal startWidth, qreal endWidth,
                                      const QPainterPath& eraserPath, qreal eraserWidth);
};

#endif // SPEEDBRUSHPATHCUTTER_H