#ifndef DRAWITEM_H
#define DRAWITEM_H

#include <QGraphicsItem>
#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QRectF>
#include <QPointF>
#include <QPainterPath>
#include <QJsonObject>
#include <QDateTime>
#include <QUuid>
#include "../utils/FeatheringRenderer.h"
#include "../core/WhiteBoardTypes.h"
#include "../data/SpeedBrushData.h"

/**
 * @brief 统一的绘制图形项类 - 替代所有Graphics类
 *
 * 核心功能：
 * 1. 支持所有绘制工具的图形类型（自由绘制、直线、矩形、椭圆等）
 * 2. 统一的绘制属性管理（画笔、画刷、抗锯齿等）
 * 3. 高性能的路径渲染
 * 4. 序列化支持
 */
class DrawItem : public QGraphicsItem
{
public:
    // 线的方向
    enum LineDirection {
        // / 斜向右上角
        DirectSlash
        ,
        // \ 斜向右下角
        ReverseSlash
    };
    // QGraphicsItem类型常量
    enum {
        DrawItemType = UserType + 1
    };

    bool has90Degrees();

    QPointF lineStartPoint();

    QPointF lineEndPoint();

    void setPath(QPainterPath path);

public:
    // 普通图形构造函数
    explicit DrawItem(const QPainterPath& path, const QPen& pen, const QBrush& brush,
                     ToolType type, QGraphicsItem* parent = nullptr);

    // 图片构造函数
    explicit DrawItem(const QString& imagePath, const QPointF& position = QPointF(0, 0),
                     qreal displayWidth = 400, qreal maxHeight = 800, QGraphicsItem* parent = nullptr);

    virtual ~DrawItem();

    // QGraphicsItem类型系统
    int type() const override { return DrawItemType; }

    // QGraphicsItem接口
    void paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget) override;
    QRectF boundingRect() const override;
    QPainterPath shape() const override;

    // 属性访问
    ToolType toolType() const { return m_toolType; }
    QPainterPath path() const { return m_path; }
    QPen pen() const { return m_pen; }
    QBrush brush() const { return m_brush; }
    qint64 timestamp() const { return m_timestamp; }
    QString itemId() const { return m_itemId; }

    // 属性设置
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    void updatePath(const QPainterPath& newPath);

    // 变换烘焙
    void bakeTransform(const QTransform& transform, const QPointF& position);

    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);

    // 内存使用估算
    qint64 memoryUsage() const;

    // 类型名称（用于调试）
    QString typeName() const;

    // ID管理
    void setItemId(const QString& id);
    static QString generateUniqueId();

    // 图片相关方法（仅当toolType() == ToolType::Image时有效）
    bool loadImage(const QString& imagePath, qreal displayWidth = 400, qreal maxHeight = 800);
    QString imagePath() const { return m_imagePath; }
    QPixmap pixmap() const { return m_pixmap; }
    QSizeF displaySize() const { return m_displaySize; }
    qreal maxHeight() const { return m_maxHeight; }
    bool isImageValid() const { return !m_pixmap.isNull(); }

    // 获取线的方向
    LineDirection lineDirection();

    // 速度笔锋接口
    void setSpeedBrushData(const SpeedBrushData& data);
    SpeedBrushData getSpeedBrushData() const;
    bool hasSpeedBrush() const;
    
    // 橡皮擦兼容性接口
    QVector<DrawItem*> cutWithEraser(const QPainterPath& eraserPath, qreal eraserWidth);

private:
    // 核心数据
    QPainterPath m_path;         // 图形路径
    QTransform m_path_transform; // 图形路径变换
    QPen m_pen;                  // 画笔属性
    QBrush m_brush;              // 画刷属性
    ToolType m_toolType;         // 图形类型
    qint64 m_timestamp;          // 创建时间戳
    QString m_itemId;            // 唯一标识符

    // 图片相关数据（仅当m_toolType == ToolType::Image时使用）
    QString m_imagePath;         // 图片文件路径
    QPixmap m_pixmap;           // 加载的图片数据
    QPixmap m_originalPixmap;   // 原始图片数据
    QSizeF m_displaySize;       // 显示尺寸
    qreal m_maxHeight;          // 最大高度限制

    QTransform m_pixmapTransform;  // 图片变换

    // 速度笔锋数据
    SpeedBrushData m_speedBrushData;  // 速度笔锋数据

    // 临时画布缓存（用于像素擦除）
    mutable QPixmap m_tempCanvas;
    mutable QRectF m_tempCanvasBounds;
    mutable bool m_tempCanvasValid;

    // 缓存的边界矩形
    mutable QRectF m_cachedBoundingRect;
    mutable bool m_boundingRectValid;

    // 辅助方法
    void invalidateBoundingRect();
    void invalidateTempCanvas();
    void initTempCanvas(QPainter* painter);
    QRectF calculateBoundingRect() const;
    void paintWithPixelClear(QPainter* painter, const QPainterPath& eraserPath);

    QString serializeTransform(QTransform transform) const;
    QTransform deserializeTransform(const QString& transformStr) const;
};

#endif // DRAWITEM_H
