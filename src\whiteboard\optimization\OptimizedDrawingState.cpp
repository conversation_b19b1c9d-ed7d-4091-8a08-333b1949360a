#include "OptimizedDrawingState.h"
#include "../tools/ShapeToolManager.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QDateTime>

OptimizedDrawingState::OptimizedDrawingState()
{
    // 初始化速度笔锋配置
    m_speedBrushConfig = WidthInterpolator::createOptimalConfig(2.0);
}

void OptimizedDrawingState::setToolType(ToolType toolType)
{
    m_toolType = toolType;
    m_pathBuilder.setToolType(toolType);
    
    // 自动启用速度笔锋（仅对FreeDraw工具）
    bool shouldEnableSpeedBrush = (toolType == ToolType::FreeDraw);
    m_pathBuilder.setSpeedBrushEnabled(shouldEnableSpeedBrush);
    m_speedBrushEnabled = shouldEnableSpeedBrush;
}

void OptimizedDrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
    m_pathBuilder.setPen(pen);  // 传递画笔信息给路径构建器
    
    // 更新速度笔锋配置的基础宽度
    if (m_speedBrushEnabled) {
        m_speedBrushConfig.baseWidth = qMax(pen.widthF(), 1.0);
        m_pathBuilder.setWidthConfig(m_speedBrushConfig);
    }
}

void OptimizedDrawingState::setBrush(const QBrush& brush)
{
    m_brush = brush;
}

void OptimizedDrawingState::startDrawing(const QPointF& startPoint)
{
    startDrawing(startPoint, QDateTime::currentMSecsSinceEpoch());
}

void OptimizedDrawingState::startDrawing(const QPointF& startPoint, qint64 timestamp)
{
    DRAWING_TIMER("OptimizedDrawingState::startDrawing");
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;

    // 重置双缓冲区域
    m_lastDrawnRegion = QRectF();
    m_currentDrawRegion = QRectF();

    // 启动路径构建（根据是否启用速度笔锋选择方法）
    if (m_speedBrushEnabled && m_toolType == ToolType::FreeDraw) {
        m_pathBuilder.startSpeedBrushPath(startPoint, timestamp, m_speedBrushConfig.baseWidth);
    } else {
        m_pathBuilder.startPath(startPoint);
    }

    // 添加初始脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        qreal penWidth = qMax(m_pen.widthF(), 2.0);
        qreal radius = qMax(penWidth + 3.0, MIN_DIRTY_REGION_SIZE / 2.0);
        m_dirtyRegionManager.addDirtyPoint(startPoint, radius);
    }
}

void OptimizedDrawingState::continueDrawing(const QPointF& point)
{
    continueDrawing(point, QDateTime::currentMSecsSinceEpoch());
}

void OptimizedDrawingState::continueDrawing(const QPointF& point, qint64 timestamp)
{
    DRAWING_TIMER("OptimizedDrawingState::continueDrawing");
    if (!m_isDrawing) {
        return;
    }

    m_lastPoint = m_currentPoint;
    m_currentPoint = point;

    // 添加点到路径构建器（根据是否启用速度笔锋选择方法）
    if (m_speedBrushEnabled && m_toolType == ToolType::FreeDraw) {
        m_pathBuilder.addSpeedBrushPoint(point, timestamp, m_speedBrushConfig.baseWidth);
    } else {
        m_pathBuilder.addPoint(point);
    }

    // 处理工具特定的绘制逻辑（包括脏区域管理）
    handleToolSpecificDrawing(point);
}

void OptimizedDrawingState::finishDrawing()
{
    finishDrawing(QDateTime::currentMSecsSinceEpoch());
}

void OptimizedDrawingState::finishDrawing(qint64 timestamp)
{
    if (!m_isDrawing) {
        return;
    }

    m_isDrawing = false;

    // 完成路径构建（根据是否启用速度笔锋选择方法）
    if (m_speedBrushEnabled && m_toolType == ToolType::FreeDraw) {
        m_pathBuilder.finishSpeedBrushPath();
    } else {
        m_pathBuilder.finishPath();
    }

    // 清理双缓冲状态和脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        m_dirtyRegionManager.clearDirtyRegions();
        m_lastDrawnRegion = QRectF();
        m_currentDrawRegion = QRectF();
    }
}

void OptimizedDrawingState::cancelDrawing()
{
    m_isDrawing = false;



    m_pathBuilder.cancelPath();
    m_dirtyRegionManager.clearDirtyRegions();
}

QPainterPath OptimizedDrawingState::getCurrentPath() const
{
    // 直接返回PathBuilder的当前路径
    return m_pathBuilder.getCurrentPath();
}

QRectF OptimizedDrawingState::getCurrentBounds() const
{
    return m_pathBuilder.getCurrentBounds();
}

bool OptimizedDrawingState::hasDirtyRegions() const
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.hasPath();
    }
    
    return m_dirtyRegionManager.hasDirtyRegions();
}

void OptimizedDrawingState::clearDirtyRegions()
{
    m_dirtyRegionManager.clearDirtyRegions();
    m_pathBuilder.clearCache();
}

QRectF OptimizedDrawingState::getDirtyRegion()
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.getCurrentBounds();
    }

    // 获取当前需要重绘的区域
    QRectF redrawRegion = m_dirtyRegionManager.getMergedDirtyRegion();

    m_lastDrawnRegion = m_currentDrawRegion;
    // 清除脏区域管理器，为下次更新做准备
    m_dirtyRegionManager.clearDirtyRegions();

    return redrawRegion;
}

bool OptimizedDrawingState::hasPath() const
{
    return m_pathBuilder.hasPath();
}

bool OptimizedDrawingState::needsUpdate() const
{
    return m_pathBuilder.needsRebuild() || hasDirtyRegions();
}

void OptimizedDrawingState::setBatchSize(int size)
{
    m_pathBuilder.setBatchSize(size);
}

void OptimizedDrawingState::setSpeedBrushEnabled(bool enabled)
{
    m_speedBrushEnabled = enabled;
    
    // 只有FreeDraw工具才能启用速度笔锋
    if (enabled && m_toolType != ToolType::FreeDraw) {
        m_speedBrushEnabled = false;
        enabled = false;
    }
    
    m_pathBuilder.setSpeedBrushEnabled(enabled);
    
    // 如果启用，确保配置已设置
    if (enabled) {
        m_pathBuilder.setWidthConfig(m_speedBrushConfig);
    }
}

bool OptimizedDrawingState::isSpeedBrushEnabled() const
{
    return m_speedBrushEnabled && m_toolType == ToolType::FreeDraw;
}

void OptimizedDrawingState::setSpeedBrushConfig(const WidthInterpolator::WidthConfig& config)
{
    m_speedBrushConfig = WidthInterpolator::validateAndFixConfig(config);
    
    // 如果速度笔锋已启用，立即应用配置
    if (m_speedBrushEnabled) {
        m_pathBuilder.setWidthConfig(m_speedBrushConfig);
    }
}

WidthInterpolator::WidthConfig OptimizedDrawingState::getSpeedBrushConfig() const
{
    return m_speedBrushConfig;
}

void OptimizedDrawingState::handleToolSpecificDrawing(const QPointF& point)
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return;
    }

    qreal penWidth = qMax(m_pen.widthF(), 1.0);
    qreal margin = penWidth + 5.0; // 增加边距确保完全覆盖

    handleShapeToolDrawing(point, margin);

}

void OptimizedDrawingState::handleShapeToolDrawing(const QPointF& point, qreal margin)
{
    // 计算当前绘制区域
    QRectF currentRegion = calculateShapeRegion(point, margin);

    // 清除脏区域管理器
    m_dirtyRegionManager.clearDirtyRegions();

    // 使用连接区域方法，确保快速移动时不会有间隙
    m_dirtyRegionManager.addConnectedRegions(m_lastDrawnRegion, currentRegion);

    // 更新记录
    m_currentDrawRegion = currentRegion;
}

QRectF OptimizedDrawingState::calculateShapeRegion(const QPointF& point, qreal margin)
{
    QRectF region;

    switch (m_toolType) {
    case ToolType::Line:
    case ToolType::DashedLine:
        // 直线：创建包含整条线的矩形，需要normalized确保正确的边界
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    case ToolType::Rectangle:
    case ToolType::Square:
    case ToolType::Ellipse:
    case ToolType::Circle:
    case ToolType::Triangle:
    case ToolType::RightTriangle:
        {
            // 形状工具：使用工具的getBoundingRect方法，它会正确处理约束
            ShapeToolManager* manager = ShapeToolManager::instance();
            if (manager && manager->hasToolType(m_toolType)) {
                AbstractShapeTool* tool = manager->getTool(m_toolType);
                if (tool) {
                    region = tool->getBoundingRect(m_startPoint, point);
                    // 确保区域是正向的，用于脏区域计算
                    region = region.normalized();
                    region = region.adjusted(-margin, -margin, margin, margin);
                    break;
                }
            }
            // 回退处理
            region = QRectF(m_startPoint, point).normalized();
            region = region.adjusted(-margin, -margin, margin, margin);
        }
        break;

    case ToolType::Arrow:
        {
            // 箭头：需要更大的边距
            region = QRectF(m_startPoint, point).normalized();
            qreal arrowMargin = margin + 30.0;
            region = region.adjusted(-arrowMargin, -arrowMargin, arrowMargin, arrowMargin);
            break;
        }
    case ToolType::FreeDraw:
        region = QRectF(m_lastPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    default:
        // 默认处理
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;
    }

    return ensureMinimumRegionSize(region);
}

QRectF OptimizedDrawingState::ensureMinimumRegionSize(const QRectF& region) const
{
    if (region.isEmpty()) {
        return region;
    }

    QRectF result = region;

    // 确保宽度至少为最小尺寸
    if (result.width() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.width()) / 2.0;
        result.setLeft(result.left() - expansion);
        result.setRight(result.right() + expansion);
    }

    // 确保高度至少为最小尺寸
    if (result.height() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.height()) / 2.0;
        result.setTop(result.top() - expansion);
        result.setBottom(result.bottom() + expansion);
    }

    return result;
}