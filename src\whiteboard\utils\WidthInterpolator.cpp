#include "WidthInterpolator.h"
#include <QJsonObject>
#include <QtMath>
#include <algorithm>

WidthInterpolator::WidthInterpolator()
{
}

qreal WidthInterpolator::calculateWidth(qreal speed, const WidthConfig& config) const
{
    if (!config.isValid()) {
        return config.baseWidth;
    }
    
    // 计算速度比例 (0.0 到 1.0)
    qreal speedRatio = calculateSpeedRatio(speed, config.maxSpeed);
    
    // 应用缓动函数使过渡更自然
    qreal easedRatio = applyEasingFunction(speedRatio);
    
    // 插值计算宽度
    qreal width = interpolateWidth(easedRatio, config);
    
    // 确保宽度在有效范围内
    return clampWidth(width, config);
}

qreal WidthInterpolator::calculateTaperLength(qreal speed, const WidthConfig& config) const
{
    if (!config.isValid()) {
        return 0.0;
    }
    
    // 速度越快，笔锋长度越长
    qreal speedRatio = calculateSpeedRatio(speed, config.maxSpeed);
    
    // 使用平方根函数使笔锋长度变化更平滑
    qreal taperRatio = qSqrt(speedRatio);
    
    return taperRatio * config.transitionLength;
}

qreal WidthInterpolator::calculateWidthWithAcceleration(qreal speed, qreal acceleration, const WidthConfig& config) const
{
    // 基础宽度计算
    qreal baseWidth = calculateWidth(speed, config);
    
    // 加速度影响因子 (加速时线条稍细，减速时稍粗)
    qreal accelerationFactor = 1.0;
    if (qAbs(acceleration) > 0.01) { // 避免除零和噪声
        // 加速度的影响应该是微妙的，不超过10%的宽度变化
        qreal maxAcceleration = config.maxSpeed / 100.0; // 假设最大加速度
        qreal normalizedAcceleration = qBound(-1.0, acceleration / maxAcceleration, 1.0);
        accelerationFactor = 1.0 - normalizedAcceleration * 0.1; // 最多10%的影响
    }
    
    return clampWidth(baseWidth * accelerationFactor, config);
}

qreal WidthInterpolator::calculateSmoothWidth(qreal speed, qreal previousWidth, const WidthConfig& config, qreal smoothingFactor) const
{
    qreal targetWidth = calculateWidth(speed, config);
    
    // 使用指数移动平均进行平滑
    smoothingFactor = qBound(0.0, smoothingFactor, 1.0);
    return previousWidth * (1.0 - smoothingFactor) + targetWidth * smoothingFactor;
}

qreal WidthInterpolator::calculateTaperRatio(qreal speed, const WidthConfig& config) const
{
    if (!config.isValid()) {
        return 0.0;
    }
    
    qreal speedRatio = calculateSpeedRatio(speed, config.maxSpeed);
    return speedRatio; // 简单的线性关系
}

qreal WidthInterpolator::calculateFeatheringIntensity(qreal speed, const WidthConfig& config) const
{
    if (!config.isValid()) {
        return 0.0;
    }
    
    // 速度越快，羽化效果越强
    qreal speedRatio = calculateSpeedRatio(speed, config.maxSpeed);
    return speedRatio * 0.5; // 最大50%的羽化强度
}

WidthInterpolator::WidthConfig WidthInterpolator::createOptimalConfig(qreal baseWidth)
{
    WidthConfig config;
    config.baseWidth = qBound(MIN_BASE_WIDTH, baseWidth, MAX_BASE_WIDTH);
    
    // 根据基础宽度调整其他参数
    if (baseWidth <= 2.0) {
        config.minWidthRatio = 0.4; // 细笔刷保持更多宽度
        config.transitionLength = 15.0;
    } else if (baseWidth <= 5.0) {
        config.minWidthRatio = 0.3;
        config.transitionLength = 20.0;
    } else {
        config.minWidthRatio = 0.25; // 粗笔刷可以更细
        config.transitionLength = 25.0;
    }
    
    config.maxSpeed = DEFAULT_MAX_SPEED;
    return config;
}

WidthInterpolator::WidthConfig WidthInterpolator::validateAndFixConfig(const WidthConfig& config)
{
    WidthConfig fixedConfig = config;
    fixedConfig.normalize();
    return fixedConfig;
}

qreal WidthInterpolator::interpolateWidth(qreal speedRatio, const WidthConfig& config) const
{
    // 线性插值：速度为0时宽度为baseWidth，速度最大时宽度为baseWidth * minWidthRatio
    qreal minWidth = config.baseWidth * config.minWidthRatio;
    return config.baseWidth - speedRatio * (config.baseWidth - minWidth);
}

qreal WidthInterpolator::calculateSpeedRatio(qreal speed, qreal maxSpeed) const
{
    if (maxSpeed <= 0.0) {
        return 0.0;
    }
    
    return qBound(0.0, speed / maxSpeed, 1.0);
}

qreal WidthInterpolator::applyEasingFunction(qreal ratio) const
{
    // 使用平滑步函数 (smoothstep) 使过渡更自然
    // smoothstep(x) = 3x² - 2x³
    return ratio * ratio * (3.0 - 2.0 * ratio);
}

qreal WidthInterpolator::clampWidth(qreal width, const WidthConfig& config) const
{
    qreal minWidth = config.baseWidth * config.minWidthRatio;
    return qBound(minWidth, width, config.baseWidth);
}

// WidthConfig 方法实现

QJsonObject WidthInterpolator::WidthConfig::toJson() const
{
    QJsonObject json;
    json["baseWidth"] = baseWidth;
    json["minWidthRatio"] = minWidthRatio;
    json["maxSpeed"] = maxSpeed;
    json["transitionLength"] = transitionLength;
    return json;
}

void WidthInterpolator::WidthConfig::fromJson(const QJsonObject& json)
{
    baseWidth = json.value("baseWidth").toDouble(2.0);
    minWidthRatio = json.value("minWidthRatio").toDouble(0.3);
    maxSpeed = json.value("maxSpeed").toDouble(5.0);
    transitionLength = json.value("transitionLength").toDouble(20.0);
    
    normalize(); // 确保数据有效
}

bool WidthInterpolator::WidthConfig::isValid() const
{
    return baseWidth >= WidthInterpolator::MIN_BASE_WIDTH && 
           baseWidth <= WidthInterpolator::MAX_BASE_WIDTH &&
           minWidthRatio >= WidthInterpolator::MIN_WIDTH_RATIO && 
           minWidthRatio <= WidthInterpolator::MAX_WIDTH_RATIO &&
           maxSpeed >= WidthInterpolator::MIN_MAX_SPEED && 
           maxSpeed <= WidthInterpolator::MAX_MAX_SPEED &&
           transitionLength >= WidthInterpolator::MIN_TRANSITION_LENGTH && 
           transitionLength <= WidthInterpolator::MAX_TRANSITION_LENGTH;
}

void WidthInterpolator::WidthConfig::normalize()
{
    baseWidth = qBound(WidthInterpolator::MIN_BASE_WIDTH, baseWidth, WidthInterpolator::MAX_BASE_WIDTH);
    minWidthRatio = qBound(WidthInterpolator::MIN_WIDTH_RATIO, minWidthRatio, WidthInterpolator::MAX_WIDTH_RATIO);
    maxSpeed = qBound(WidthInterpolator::MIN_MAX_SPEED, maxSpeed, WidthInterpolator::MAX_MAX_SPEED);
    transitionLength = qBound(WidthInterpolator::MIN_TRANSITION_LENGTH, transitionLength, WidthInterpolator::MAX_TRANSITION_LENGTH);
}