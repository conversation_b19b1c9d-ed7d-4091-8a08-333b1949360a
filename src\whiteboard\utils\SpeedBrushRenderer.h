#ifndef SPEEDBRUSHRENDERER_H
#define SPEEDBRUSHRENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QVector>
#include "../optimization/IncrementalPathBuilder.h"
#include "../core/WhiteBoardTypes.h"
#include "FeatheringRenderer.h"

/**
 * @brief 速度笔锋渲染器 - 处理变宽度路径渲染
 * 
 * 核心功能：
 * 1. 变宽度描边路径生成算法
 * 2. 集成FeatheringRenderer提供平滑效果
 * 3. 优化渲染性能和视觉效果
 * 4. 支持速度感知的笔锋渲染
 */
class SpeedBrushRenderer
{
public:
    SpeedBrushRenderer() = default;
    ~SpeedBrushRenderer() = default;

    /**
     * @brief 渲染速度笔锋路径
     * @param painter 绘制器
     * @param points 速度笔锋点数据
     * @param basePen 基础画笔
     * @param brush 画刷
     * @param toolType 工具类型
     */
    static void renderSpeedBrushPath(QPainter* painter, 
                                   const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
                                   const QPen& basePen,
                                   const QBrush& brush,
                                   ToolType toolType);

    /**
     * @brief 渲染速度笔锋路径（使用预构建的路径）
     * @param painter 绘制器
     * @param path 预构建的变宽度路径
     * @param points 速度笔锋点数据（用于优化）
     * @param basePen 基础画笔
     * @param brush 画刷
     * @param toolType 工具类型
     */
    static void renderSpeedBrushPath(QPainter* painter, 
                                   const QPainterPath& path,
                                   const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
                                   const QPen& basePen,
                                   const QBrush& brush,
                                   ToolType toolType);

private:
    /**
     * @brief 创建变宽度描边路径
     * @param points 速度笔锋点数据
     * @return 变宽度描边路径
     */
    static QPainterPath createVariableWidthStroke(
        const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points);

    /**
     * @brief 创建单个路径段的变宽度描边
     * @param start 起始点
     * @param end 结束点
     * @return 路径段的描边路径
     */
    static QPainterPath createSegmentStroke(
        const IncrementalPathBuilder::SpeedBrushPoint& start,
        const IncrementalPathBuilder::SpeedBrushPoint& end);

    /**
     * @brief 使用羽化效果渲染路径
     * @param painter 绘制器
     * @param strokePath 描边路径
     * @param pen 画笔
     * @param brush 画刷
     * @param toolType 工具类型
     */
    static void renderWithFeathering(QPainter* painter, 
                                   const QPainterPath& strokePath,
                                   const QPen& pen, 
                                   const QBrush& brush,
                                   ToolType toolType);

    /**
     * @brief 计算两点间的垂直向量
     * @param start 起始点
     * @param end 结束点
     * @param width 宽度
     * @return 垂直向量
     */
    static QPointF calculatePerpendicularVector(const QPointF& start, const QPointF& end, qreal width);

    /**
     * @brief 优化路径点数据
     * @param points 原始点数据
     * @return 优化后的点数据
     */
    static QVector<IncrementalPathBuilder::SpeedBrushPoint> optimizePoints(
        const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points);

    /**
     * @brief 检查是否需要路径简化
     * @param points 点数据
     * @return 是否需要简化
     */
    static bool needsPathSimplification(const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points);

    /**
     * @brief 简化路径点数据
     * @param points 原始点数据
     * @param tolerance 简化容差
     * @return 简化后的点数据
     */
    static QVector<IncrementalPathBuilder::SpeedBrushPoint> simplifyPoints(
        const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points, qreal tolerance);

    // 性能优化常量
    static constexpr int MAX_POINTS_FOR_FULL_QUALITY = 500;    // 全质量渲染的最大点数
    static constexpr qreal SIMPLIFICATION_TOLERANCE = 1.0;     // 路径简化容差
    static constexpr qreal MIN_SEGMENT_LENGTH = 0.5;           // 最小段长度
    static constexpr qreal MAX_WIDTH_RATIO = 10.0;             // 最大宽度比例
};

#endif // SPEEDBRUSHRENDERER_H