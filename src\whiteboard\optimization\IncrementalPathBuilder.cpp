#include "IncrementalPathBuilder.h"
#include "../tools/ShapeToolManager.h"
#include "../core/WhiteBoardTypes.h"
#include <QtMath>
#include <QDebug>



// PathSegment 实现
void IncrementalPathBuilder::PathSegment::addPoint(const QPointF& point)
{
    points.append(point);

    // 优化的增量边界计算：直接扩展现有边界而不是使用united()
    if (boundingRect.isNull()) {
        // 首次添加点：创建初始边界矩形
        boundingRect = QRectF(point.x(), point.y(), 1.0, 1.0);
    } else {
        // 增量扩展边界：只检查和更新需要扩展的边
        qreal left = boundingRect.left();
        qreal top = boundingRect.top();
        qreal right = boundingRect.right();
        qreal bottom = boundingRect.bottom();

        // 检查是否需要扩展边界
        if (point.x() < left) {
            left = point.x();
        } else if (point.x() > right) {
            right = point.x();
        }

        if (point.y() < top) {
            top = point.y();
        } else if (point.y() > bottom) {
            bottom = point.y();
        }

        // 只有当边界确实需要扩展时才更新
        if (left != boundingRect.left() || top != boundingRect.top() ||
            right != boundingRect.right() || bottom != boundingRect.bottom()) {
            boundingRect.setCoords(left, top, right, bottom);
        }
    }
    needsRebuild = true;
}

void IncrementalPathBuilder::PathSegment::clear()
{
    points.clear();
    boundingRect = QRectF();
    needsRebuild = true;
}

// IncrementalPathBuilder 实现
IncrementalPathBuilder::IncrementalPathBuilder()
{
    // 初始化默认宽度配置
    m_widthConfig = WidthInterpolator::createOptimalConfig(2.0);
}

void IncrementalPathBuilder::startPath(const QPointF& startPoint)
{
    m_currentPath = QPainterPath();
    m_currentPath.moveTo(startPoint);

    m_pendingSegment.clear();
    m_pendingSegment.addPoint(startPoint);

    m_isBuilding = true;
    m_needsRebuild = false;
    m_lastPoint = startPoint;
    m_startPoint = startPoint;  // 保存起始点

    // 重置速度笔锋数据
    resetSpeedBrushData();

    // 初始化延迟边界计算状态
    m_incrementalBounds = QRectF(startPoint.x(), startPoint.y(), 1.0, 1.0);
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

void IncrementalPathBuilder::addPoint(const QPointF& point)
{
    if (!m_isBuilding) {
        return;
    }



    // 根据工具类型处理点
    if (isFreeDrawTool(m_currentToolType) || m_currentToolType == ToolType::Lasso) {
        handleFreeDrawPoint(point);
    } else {
        handleShapePoint(point);
    }

    m_lastPoint = point;
    updateIncrementalBounds(point);
}

void IncrementalPathBuilder::finishPath()
{
    if (!m_isBuilding) {
        return;
    }

    // 刷新待处理的段
    flushPendingSegment();

    if (m_dashConversionEnabled && !m_currentPath.isEmpty() && m_dashConverter.needsConversion(m_currentPen)) {
        QPainterPath solidPath = m_dashConverter.convertDashToSolid(m_currentPath, m_currentPen);
        if (!solidPath.isEmpty()) {
            m_currentPath = solidPath;
        }
    }


    m_isBuilding = false;
    m_needsRebuild = false;
}

void IncrementalPathBuilder::cancelPath()
{
    m_currentPath = QPainterPath();
    m_pendingSegment.clear();
    m_isBuilding = false;
    m_needsRebuild = false;
    m_incrementalBounds = QRectF();

    // 重置速度笔锋数据
    resetSpeedBrushData();

    // 重置延迟边界计算状态
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

QPainterPath IncrementalPathBuilder::getCurrentPath() const
{
    if (m_needsRebuild) {
        // 需要重建路径时，创建临时路径
        QPainterPath tempPath = m_currentPath;
        
        // 添加待处理段的点
        if (!m_pendingSegment.isEmpty()) {
            for (const QPointF& point : m_pendingSegment.points) {
                tempPath.lineTo(point);
            }
        }
        
        return tempPath;
    }
    
    return m_currentPath;
}

QRectF IncrementalPathBuilder::getCurrentBounds() const
{
    // 延迟计算：只在需要时计算当前路径边界
    if (!m_currentBoundsValid) {
        m_cachedCurrentBounds = getCurrentPath().boundingRect();
        m_currentBoundsValid = true;
    }
    return m_cachedCurrentBounds;
}

QRectF IncrementalPathBuilder::getIncrementalBounds() const
{
    // 延迟更新增量边界
    updateBoundsIfNeeded();
    return m_incrementalBounds;
}

void IncrementalPathBuilder::rebuildPath()
{
    if (!m_needsRebuild) {
        return;
    }
    
    // 刷新待处理的段到主路径
    flushPendingSegment();
    m_needsRebuild = false;
}

void IncrementalPathBuilder::clearCache()
{
    m_pendingSegment.clear();
    m_incrementalBounds = QRectF();
    m_needsRebuild = false;

    // 重置延迟边界计算状态
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

void IncrementalPathBuilder::addPointToPendingSegment(const QPointF& point)
{
    m_pendingSegment.addPoint(point);
    m_needsRebuild = true;
    
    // 当待处理段达到批量大小时，刷新到主路径
    if (m_pendingSegment.pointCount() >= m_batchSize) {
        flushPendingSegment();
    }
}

void IncrementalPathBuilder::flushPendingSegment()
{
    if (m_pendingSegment.isEmpty()) {
        return;
    }

    // 将待处理段的点添加到主路径
    for (const QPointF& point : m_pendingSegment.points) {
        m_currentPath.lineTo(point);
    }

    m_pendingSegment.clear();
    m_needsRebuild = false;

    // 路径发生变化，使边界缓存失效
    invalidateBoundsCache();
}

void IncrementalPathBuilder::updateIncrementalBounds(const QPointF& point)
{
    Q_UNUSED(point)  // 参数未使用，但保留接口兼容性

    // 标记边界需要更新，但不立即计算
    m_boundsNeedUpdate = true;

    // 使边界缓存失效
    invalidateBoundsCache();
}

// 延迟边界计算的实现方法
void IncrementalPathBuilder::invalidateBoundsCache() const
{
    m_currentBoundsValid = false;
}

void IncrementalPathBuilder::updateBoundsIfNeeded() const
{
    if (!m_boundsNeedUpdate) {
        return;
    }

    // 只有在真正需要边界时才进行计算
    // 这里可以根据实际需求决定是否立即计算
    // 对于高频调用的场景，可以进一步延迟到真正使用时
    m_boundsNeedUpdate = false;
}

void IncrementalPathBuilder::handleFreeDrawPoint(const QPointF& point)
{
    // 自由绘制：直接添加点到待处理段
    addPointToPendingSegment(point);
}

void IncrementalPathBuilder::handleShapePoint(const QPointF& point)
{
    ShapeToolManager* manager = ShapeToolManager::instance();

    if (manager && manager->hasToolType(m_currentToolType)) {
        m_currentPath = manager->createPath(m_currentToolType, m_startPoint, point);
        m_pendingSegment.clear();
        m_needsRebuild = false;
    } else {
        m_currentPath = QPainterPath();
        m_currentPath.moveTo(m_startPoint);
        m_currentPath.lineTo(point);
        m_pendingSegment.clear();
        m_needsRebuild = false;
    }
}

// 速度笔锋方法实现

void IncrementalPathBuilder::startSpeedBrushPath(const QPointF& startPoint, qint64 timestamp, qreal baseWidth)
{
    // 首先调用标准的路径开始方法
    startPath(startPoint);
    
    // 如果启用了速度笔锋，初始化速度笔锋数据
    if (m_speedBrushEnabled && isFreeDrawTool(m_currentToolType)) {
        // 更新宽度配置的基础宽度
        m_widthConfig.baseWidth = baseWidth;
        m_widthConfig = WidthInterpolator::validateAndFixConfig(m_widthConfig);
        
        // 重置速度计算器
        m_speedCalculator.reset();
        
        // 添加起始点到速度笔锋数据
        m_speedBrushPoints.clear();
        m_speedBrushPoints.append(SpeedBrushPoint(startPoint, baseWidth, 0.0, timestamp));
        
        // 添加起始点到速度计算器
        m_speedCalculator.addPoint(startPoint, timestamp);
    }
}

void IncrementalPathBuilder::addSpeedBrushPoint(const QPointF& point, qint64 timestamp, qreal baseWidth)
{
    if (!m_isBuilding) {
        return;
    }
    
    // 如果启用了速度笔锋且是自由绘制工具，使用速度感知的点添加
    if (m_speedBrushEnabled && isFreeDrawTool(m_currentToolType)) {
        addSpeedBrushPointInternal(point, timestamp, baseWidth);
    } else {
        // 否则使用标准的点添加方法
        addPoint(point);
    }
}

void IncrementalPathBuilder::finishSpeedBrushPath()
{
    finishPath();
    
    // 如果有速度笔锋数据，确保最后一个点的宽度回到基础宽度
    if (m_speedBrushEnabled && !m_speedBrushPoints.isEmpty()) {
        SpeedBrushPoint& lastPoint = m_speedBrushPoints.last();
        lastPoint.width = m_widthConfig.baseWidth;
        lastPoint.speed = 0.0; // 结束时速度为0
    }
}

QPainterPath IncrementalPathBuilder::getVariableWidthPath() const
{
    if (!m_speedBrushEnabled || m_speedBrushPoints.isEmpty()) {
        return getCurrentPath();
    }
    
    return createVariableWidthPath();
}

QVector<IncrementalPathBuilder::SpeedBrushPoint> IncrementalPathBuilder::getSpeedBrushPoints() const
{
    return m_speedBrushPoints;
}

bool IncrementalPathBuilder::hasSpeedBrushData() const
{
    return m_speedBrushEnabled && !m_speedBrushPoints.isEmpty();
}

void IncrementalPathBuilder::addSpeedBrushPointInternal(const QPointF& point, qint64 timestamp, qreal baseWidth)
{
    // 更新基础宽度配置
    if (qAbs(m_widthConfig.baseWidth - baseWidth) > 0.01) {
        m_widthConfig.baseWidth = baseWidth;
        m_widthConfig = WidthInterpolator::validateAndFixConfig(m_widthConfig);
    }
    
    // 添加点到速度计算器
    m_speedCalculator.addPoint(point, timestamp);
    
    // 获取当前速度数据
    SpeedCalculator::SpeedData speedData = m_speedCalculator.getCurrentSpeed();
    
    // 计算该点的宽度
    qreal width = m_widthInterpolator.calculateWidth(speedData.smoothedSpeed, m_widthConfig);
    
    // 如果有前一个点，进行宽度平滑
    if (!m_speedBrushPoints.isEmpty()) {
        qreal previousWidth = m_speedBrushPoints.last().width;
        width = m_widthInterpolator.calculateSmoothWidth(speedData.smoothedSpeed, previousWidth, m_widthConfig, 0.3);
    }
    
    // 添加速度笔锋点
    m_speedBrushPoints.append(SpeedBrushPoint(point, width, speedData.smoothedSpeed, timestamp));
    
    // 同时添加到标准路径构建流程以保持兼容性
    handleFreeDrawPoint(point);
    
    // 更新边界
    updateIncrementalBounds(point);
}

QPainterPath IncrementalPathBuilder::createVariableWidthPath() const
{
    if (m_speedBrushPoints.size() < 2) {
        return getCurrentPath(); // 回退到标准路径
    }
    
    QPainterPath variablePath;
    
    // 从第一个点开始
    const SpeedBrushPoint& firstPoint = m_speedBrushPoints.first();
    variablePath.moveTo(firstPoint.position);
    
    // 为每个相邻的点对创建锥形段
    for (int i = 1; i < m_speedBrushPoints.size(); ++i) {
        const SpeedBrushPoint& startPoint = m_speedBrushPoints[i-1];
        const SpeedBrushPoint& endPoint = m_speedBrushPoints[i];
        
        QPainterPath segment = createTaperedSegment(startPoint, endPoint);
        if (!segment.isEmpty()) {
            variablePath.addPath(segment);
        }
    }
    
    return variablePath;
}

QPainterPath IncrementalPathBuilder::createTaperedSegment(const SpeedBrushPoint& start, const SpeedBrushPoint& end) const
{
    QPainterPath segment;
    
    // 计算方向向量
    QPointF direction = end.position - start.position;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
    
    if (length < 0.1) {
        return segment; // 点太近，跳过
    }
    
    // 标准化方向向量
    direction /= length;
    
    // 计算垂直向量
    QPointF perpendicular(-direction.y(), direction.x());
    
    // 创建锥形路径
    qreal startHalfWidth = start.width * 0.5;
    qreal endHalfWidth = end.width * 0.5;
    
    // 计算四个角点
    QPointF startLeft = start.position + perpendicular * startHalfWidth;
    QPointF startRight = start.position - perpendicular * startHalfWidth;
    QPointF endLeft = end.position + perpendicular * endHalfWidth;
    QPointF endRight = end.position - perpendicular * endHalfWidth;
    
    // 构建锥形路径
    segment.moveTo(startLeft);
    segment.lineTo(endLeft);
    segment.lineTo(endRight);
    segment.lineTo(startRight);
    segment.closeSubpath();
    
    return segment;
}

void IncrementalPathBuilder::resetSpeedBrushData()
{
    m_speedBrushPoints.clear();
    m_speedCalculator.reset();
}




