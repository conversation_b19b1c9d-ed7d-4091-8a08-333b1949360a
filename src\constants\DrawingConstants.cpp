#include "DrawingConstants.h"
#include <QMutexLocker>

// 静态成员变量定义
bool DrawingConstants::s_eraseEnabled = false;
QPainterPath DrawingConstants::s_currentEraserPath;
qreal DrawingConstants::s_eraserWidth = 10.0;
QMutex DrawingConstants::s_mutex;

bool DrawingConstants::getEraseStatus()
{
    QMutexLocker locker(&s_mutex);
    return s_eraseEnabled;
}

void DrawingConstants::setEraseStatus(bool enabled)
{
    QMutexLocker locker(&s_mutex);
    s_eraseEnabled = enabled;
    
    // 当禁用橡皮擦时，清除当前路径
    if (!enabled) {
        s_currentEraserPath = QPainterPath();
    }
}

QPainterPath DrawingConstants::getCurrentEraserPath()
{
    QMutexLocker locker(&s_mutex);
    return s_currentEraserPath;
}

void DrawingConstants::setCurrentEraserPath(const QPainterPath& path)
{
    QMutexLocker locker(&s_mutex);
    s_currentEraserPath = path;
}

void DrawingConstants::clearEraserPath()
{
    QMutexLocker locker(&s_mutex);
    s_currentEraserPath = QPainterPath();
}

qreal DrawingConstants::getEraserWidth()
{
    QMutexLocker locker(&s_mutex);
    return s_eraserWidth;
}

void DrawingConstants::setEraserWidth(qreal width)
{
    QMutexLocker locker(&s_mutex);
    s_eraserWidth = qMax(1.0, width);  // 确保宽度至少为1
}