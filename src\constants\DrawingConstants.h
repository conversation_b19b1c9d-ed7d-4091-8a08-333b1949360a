#ifndef DRAWINGCONSTANTS_H
#define DRAWINGCONSTANTS_H

#include <QPainterPath>
#include <QMutex>

/**
 * @brief 绘制常量和全局状态管理类
 * 
 * 管理白板绘制过程中的全局状态，包括橡皮擦状态和路径信息
 */
class DrawingConstants
{
public:
    // 橡皮擦状态管理
    static bool getEraseStatus();
    static void setEraseStatus(bool enabled);
    
    // 橡皮擦路径管理
    static QPainterPath getCurrentEraserPath();
    static void setCurrentEraserPath(const QPainterPath& path);
    static void clearEraserPath();
    
    // 橡皮擦配置
    static qreal getEraserWidth();
    static void setEraserWidth(qreal width);
    
private:
    static bool s_eraseEnabled;
    static QPainterPath s_currentEraserPath;
    static qreal s_eraserWidth;
    static QMutex s_mutex;  // 线程安全保护
};

#endif // DRAWINGCONSTANTS_H