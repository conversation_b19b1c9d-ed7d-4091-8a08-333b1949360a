#ifndef SPEEDBRUSHDATA_H
#define SPEEDBRUSHDATA_H

#include <QJsonObject>
#include <QVector>
#include "../optimization/IncrementalPathBuilder.h"
#include "../utils/WidthInterpolator.h"

/**
 * @brief 速度笔锋数据结构 - 存储笔锋相关数据
 * 
 * 核心功能：
 * 1. 存储速度笔锋点数据
 * 2. 管理宽度配置信息
 * 3. 提供序列化和反序列化支持
 * 4. 内存使用估算
 */
struct SpeedBrushData
{
    bool enabled = false;                                           // 是否启用速度笔锋
    QVector<IncrementalPathBuilder::SpeedBrushPoint> points;       // 速度笔锋点数据
    WidthInterpolator::WidthConfig widthConfig;                   // 宽度配置
    
    // 构造函数
    SpeedBrushData() = default;
    SpeedBrushData(bool enable, const WidthInterpolator::WidthConfig& config)
        : enabled(enable), widthConfig(config) {}
    
    // 数据操作
    void addPoint(const IncrementalPathBuilder::SpeedBrushPoint& point);
    void clear();
    bool isEmpty() const { return points.isEmpty(); }
    int pointCount() const { return points.size(); }
    
    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
    // 数据验证
    bool isValid() const;
    void normalize();
    
    // 便利方法
    QVector<IncrementalPathBuilder::SpeedBrushPoint> getPoints() const { return points; }
    WidthInterpolator::WidthConfig getWidthConfig() const { return widthConfig; }
    void setWidthConfig(const WidthInterpolator::WidthConfig& config) { widthConfig = config; }
    void setPoints(const QVector<IncrementalPathBuilder::SpeedBrushPoint>& newPoints) { points = newPoints; }
    
private:
    // 内部验证方法
    bool validatePoints() const;
    bool validateWidthConfig() const;
};

#endif // SPEEDBRUSHDATA_H