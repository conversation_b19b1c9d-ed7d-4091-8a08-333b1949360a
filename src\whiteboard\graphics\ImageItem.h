#ifndef IMAGEITEM_H
#define IMAGEITEM_H

#include <QGraphicsItem>
#include <QPixmap>
#include <QPainter>
#include <QRectF>
#include <QPointF>
#include <QJsonObject>
#include <QDateTime>
#include <QString>

/**
 * @brief 图片图形项类 - 专门用于在白板中显示图片
 *
 * 核心功能：
 * 1. 支持本地图片文件加载和显示
 * 2. 自动尺寸适配和比例保持
 * 3. 支持拖拽定位和缩放
 * 4. 序列化支持（保存图片路径和显示参数）
 * 5. 屏幕适配支持
 */
class ImageItem : public QGraphicsItem
{
public:
    // QGraphicsItem类型常量
    enum {
        ImageItemType = UserType + 2
    };

    /**
     * @brief 构造函数
     * @param imagePath 图片文件路径
     * @param position 图片位置
     * @param displayWidth 显示宽度（0表示使用默认宽度）
     * @param maxHeight 最大高度限制
     * @param parent 父图形项
     */
    explicit ImageItem(const QString& imagePath, 
                      const QPointF& position = QPointF(0, 0),
                      qreal displayWidth = 0,
                      qreal maxHeight = 800,
                      QGraphicsItem* parent = nullptr);

    // QGraphicsItem类型系统
    int type() const override { return ImageItemType; }

    // QGraphicsItem接口
    void paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget) override;
    QRectF boundingRect() const override;
    QPainterPath shape() const override;

    // 属性访问
    QString imagePath() const { return m_imagePath; }
    QPixmap pixmap() const { return m_pixmap; }
    QSizeF displaySize() const { return m_displaySize; }
    qreal displayWidth() const { return m_displaySize.width(); }
    qreal displayHeight() const { return m_displaySize.height(); }
    qreal maxHeight() const { return m_maxHeight; }
    bool isValid() const { return !m_pixmap.isNull(); }
    qint64 timestamp() const { return m_timestamp; }

    // 属性设置
    bool setImagePath(const QString& imagePath);
    void setDisplayWidth(qreal width);
    void setMaxHeight(qreal maxHeight);
    void setPosition(const QPointF& position);

    // 尺寸计算
    QSizeF calculateOptimalSize(qreal targetWidth = 0) const;
    void updateDisplaySize();

    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);

    // 调试信息
    QString debugInfo() const;

private:
    // 核心数据
    QString m_imagePath;         // 图片文件路径
    QPixmap m_pixmap;           // 加载的图片数据
    QSizeF m_displaySize;       // 显示尺寸
    qreal m_maxHeight;          // 最大高度限制
    qint64 m_timestamp;         // 创建时间戳

    // 默认参数
    static const qreal DEFAULT_WIDTH;      // 默认显示宽度
    static const qreal DEFAULT_MAX_HEIGHT; // 默认最大高度

    // 缓存的边界矩形
    mutable QRectF m_cachedBoundingRect;
    mutable bool m_boundingRectValid;

    // 辅助方法
    bool loadImage(const QString& imagePath);
    void invalidateBoundingRect();
    QRectF calculateBoundingRect() const;
    QSizeF calculateSizeWithConstraints(const QSizeF& originalSize, qreal targetWidth) const;
};

#endif // IMAGEITEM_H
