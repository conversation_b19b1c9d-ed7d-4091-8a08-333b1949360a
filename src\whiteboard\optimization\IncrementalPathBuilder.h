#ifndef INCREMENTALPATHBUILDER_H
#define INCREMENTALPATHBUILDER_H

#include <QPainterPath>
#include <QPointF>
#include <QVector>
#include <QRectF>
#include "../core/WhiteBoardTypes.h"

#include "../utils/DashPathConverter.h"
#include "../utils/SpeedCalculator.h"
#include "../utils/WidthInterpolator.h"



/**
 * @brief 增量路径构建器 - 优化路径绘制性能
 *
 * 核心功能：
 * 1. 避免每次移动都重建完整路径
 * 2. 使用分段缓存和批量更新策略
 * 3. 支持不同工具类型的优化策略
 * 4. 支持速度笔锋效果的变宽度路径构建
 */
class IncrementalPathBuilder
{
public:
    /**
     * @brief 路径段结构 - 缓存路径片段
     */
    struct PathSegment {
        QVector<QPointF> points;        // 路径点集合
        QRectF boundingRect;            // 边界矩形
        bool needsRebuild = true;       // 是否需要重建

        void addPoint(const QPointF& point);
        void clear();
        bool isEmpty() const { return points.isEmpty(); }
        int pointCount() const { return points.size(); }
    };

    /**
     * @brief 速度笔锋点结构 - 存储带速度信息的点
     */
    struct SpeedBrushPoint {
        QPointF position;               // 点位置
        qreal width;                    // 该点的宽度
        qreal speed;                    // 该点的速度
        qint64 timestamp;               // 时间戳
        
        SpeedBrushPoint() = default;
        SpeedBrushPoint(const QPointF& pos, qreal w, qreal s, qint64 t)
            : position(pos), width(w), speed(s), timestamp(t) {}
    };

public:
    IncrementalPathBuilder();
    ~IncrementalPathBuilder() = default;

    // 路径构建
    void startPath(const QPointF& startPoint);
    void addPoint(const QPointF& point);
    void finishPath();
    void cancelPath();

    // 速度感知的路径构建
    void startSpeedBrushPath(const QPointF& startPoint, qint64 timestamp, qreal baseWidth);
    void addSpeedBrushPoint(const QPointF& point, qint64 timestamp, qreal baseWidth);
    void finishSpeedBrushPath();



    // 获取路径
    QPainterPath getCurrentPath() const;
    QRectF getCurrentBounds() const;
    QRectF getIncrementalBounds() const;  // 获取自上次更新以来的边界

    // 速度笔锋路径获取
    QPainterPath getVariableWidthPath() const;
    QVector<SpeedBrushPoint> getSpeedBrushPoints() const;
    bool hasSpeedBrushData() const;

    // 配置
    void setBatchSize(int size) { m_batchSize = size; }
    void setToolType(ToolType toolType) { m_currentToolType = toolType; }

    // 速度笔锋配置
    void setSpeedBrushEnabled(bool enabled) { m_speedBrushEnabled = enabled; }
    void setWidthConfig(const WidthInterpolator::WidthConfig& config) { m_widthConfig = config; }
    bool isSpeedBrushEnabled() const { return m_speedBrushEnabled; }



    // 画笔配置
    void setPen(const QPen& pen) { m_currentPen = pen; }
    void setDashConversionEnabled(bool enabled) { m_dashConversionEnabled = enabled; }



    // 状态查询
    bool isBuilding() const { return m_isBuilding; }
    bool hasPath() const { return !m_currentPath.isEmpty(); }
    bool needsRebuild() const { return m_needsRebuild; }
    int getPendingPointCount() const { return m_pendingSegment.pointCount(); }

    // 性能优化
    void rebuildPath();
    void clearCache();

private:
    // 核心数据
    QPainterPath m_currentPath;
    PathSegment m_pendingSegment;

    // 速度笔锋数据
    QVector<SpeedBrushPoint> m_speedBrushPoints;
    SpeedCalculator m_speedCalculator;
    WidthInterpolator m_widthInterpolator;
    WidthInterpolator::WidthConfig m_widthConfig;
    bool m_speedBrushEnabled = false;

    // 状态
    bool m_isBuilding = false;
    bool m_needsRebuild = false;
    QPointF m_lastPoint;

    // 延迟边界计算优化
    mutable QRectF m_incrementalBounds;     // 增量边界缓存
    mutable bool m_boundsNeedUpdate = false; // 边界是否需要更新
    mutable QRectF m_cachedCurrentBounds;   // 当前路径边界缓存
    mutable bool m_currentBoundsValid = false; // 当前边界缓存是否有效

    // 配置
    int m_batchSize = 5;
    ToolType m_currentToolType = ToolType::FreeDraw;
    QPointF m_startPoint;  // 保存起始点用于形状工具

    // 内部方法
    void addPointToPendingSegment(const QPointF& point);
    void flushPendingSegment();
    void updateIncrementalBounds(const QPointF& point);

    // 延迟边界计算方法
    void invalidateBoundsCache() const;
    void updateBoundsIfNeeded() const;

    // 工具特定处理
    void handleFreeDrawPoint(const QPointF& point);
    void handleShapePoint(const QPointF& point);

    // 速度笔锋内部方法
    void addSpeedBrushPointInternal(const QPointF& point, qint64 timestamp, qreal baseWidth);
    QPainterPath createVariableWidthPath() const;
    QPainterPath createTaperedSegment(const SpeedBrushPoint& start, const SpeedBrushPoint& end) const;
    void resetSpeedBrushData();



    // 虚线转换器
    DashPathConverter m_dashConverter;
    QPen m_currentPen;
    bool m_dashConversionEnabled = true;


};

#endif // INCREMENTALPATHBUILDER_H
