#ifndef SPEEDCALCULATOR_H
#define SPEEDCALCULATOR_H

#include <QPointF>
#include <QVector>
#include <QtGlobal>

/**
 * @brief 速度计算器 - 计算绘制速度并提供平滑的速度值
 * 
 * 核心功能：
 * 1. 基于点位历史和时间戳计算绘制速度
 * 2. 提供速度平滑算法减少抖动
 * 3. 检测静止状态
 * 4. 支持加速度计算
 */
class SpeedCalculator
{
public:
    /**
     * @brief 速度数据结构
     */
    struct SpeedData {
        qreal currentSpeed = 0.0;      // 当前速度 (像素/毫秒)
        qreal smoothedSpeed = 0.0;     // 平滑后的速度
        qreal acceleration = 0.0;      // 加速度 (像素/毫秒²)
        bool isStationary = true;      // 是否静止
        
        SpeedData() = default;
        SpeedData(qreal current, qreal smoothed, qreal accel, bool stationary)
            : currentSpeed(current), smoothedSpeed(smoothed), 
              acceleration(accel), isStationary(stationary) {}
    };

    /**
     * @brief 历史点数据结构
     */
    struct HistoryPoint {
        QPointF position;
        qint64 timestamp;
        qreal speed = 0.0;
        
        HistoryPoint() = default;
        HistoryPoint(const QPointF& pos, qint64 time)
            : position(pos), timestamp(time) {}
        HistoryPoint(const QPointF& pos, qint64 time, qreal spd)
            : position(pos), timestamp(time), speed(spd) {}
    };

public:
    SpeedCalculator();
    ~SpeedCalculator() = default;

    // 主要接口
    void addPoint(const QPointF& point, qint64 timestamp);
    SpeedData getCurrentSpeed() const;
    void reset();

    // 配置接口
    void setSmoothingFactor(qreal factor);
    void setStationaryThreshold(qreal threshold);
    void setHistorySize(int size);

    // 查询接口
    bool hasValidSpeed() const;
    int getHistoryCount() const { return m_history.size(); }
    qreal getAverageSpeed() const;
    qreal getMaxSpeed() const;

private:
    // 常量配置
    static constexpr int DEFAULT_HISTORY_SIZE = 5;
    static constexpr qreal DEFAULT_SMOOTHING_FACTOR = 0.3;
    static constexpr qreal DEFAULT_STATIONARY_THRESHOLD = 0.1; // 像素/毫秒
    static constexpr qreal MIN_TIME_DELTA = 1.0; // 最小时间间隔(毫秒)

    // 历史数据
    QVector<HistoryPoint> m_history;
    int m_maxHistorySize;

    // 配置参数
    qreal m_smoothingFactor;
    qreal m_stationaryThreshold;

    // 缓存的计算结果
    mutable SpeedData m_cachedSpeedData;
    mutable bool m_speedDataValid = false;

    // 内部计算方法
    qreal calculateInstantaneousSpeed(const HistoryPoint& current, const HistoryPoint& previous) const;
    qreal calculateSmoothedSpeed(qreal currentSpeed) const;
    qreal calculateAcceleration() const;
    bool isCurrentlyStationary(qreal speed) const;
    void updateSpeedCache() const;
    void addToHistory(const QPointF& point, qint64 timestamp);
    void trimHistory();
};

#endif // SPEEDCALCULATOR_H