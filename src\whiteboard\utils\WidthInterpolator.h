#ifndef WIDTHINTERPOLATOR_H
#define WIDTHINTERPOLATOR_H

#include <QtGlobal>
#include <QJsonObject>

/**
 * @brief 宽度插值器 - 根据速度计算动态线条宽度
 * 
 * 核心功能：
 * 1. 根据绘制速度计算动态线条宽度
 * 2. 实现笔锋长度计算
 * 3. 提供宽度配置管理
 * 4. 支持平滑的宽度过渡
 */
class WidthInterpolator
{
public:
    /**
     * @brief 宽度配置结构
     */
    struct WidthConfig {
        qreal baseWidth = 2.0;           // 基础宽度（用户设置的宽度）
        qreal minWidthRatio = 0.3;       // 最小宽度比例（相对于基础宽度）
        qreal maxSpeed = 5.0;            // 最大速度阈值 (像素/毫秒)
        qreal transitionLength = 20.0;   // 笔锋过渡长度 (像素)
        
        WidthConfig() = default;
        WidthConfig(qreal base, qreal minRatio = 0.3, qreal maxSpd = 5.0, qreal transition = 20.0)
            : baseWidth(base), minWidthRatio(minRatio), maxSpeed(maxSpd), transitionLength(transition) {}
        
        // 序列化支持
        QJsonObject toJson() const;
        void fromJson(const QJsonObject& json);
        
        // 验证配置有效性
        bool isValid() const;
        void normalize();
    };

public:
    WidthInterpolator();
    ~WidthInterpolator() = default;

    // 主要计算接口
    qreal calculateWidth(qreal speed, const WidthConfig& config) const;
    qreal calculateTaperLength(qreal speed, const WidthConfig& config) const;
    
    // 高级计算接口
    qreal calculateWidthWithAcceleration(qreal speed, qreal acceleration, const WidthConfig& config) const;
    qreal calculateSmoothWidth(qreal speed, qreal previousWidth, const WidthConfig& config, qreal smoothingFactor = 0.2) const;
    
    // 笔锋效果计算
    qreal calculateTaperRatio(qreal speed, const WidthConfig& config) const;
    qreal calculateFeatheringIntensity(qreal speed, const WidthConfig& config) const;
    
    // 配置验证和优化
    static WidthConfig createOptimalConfig(qreal baseWidth);
    static WidthConfig validateAndFixConfig(const WidthConfig& config);

private:
    // 默认配置常量
    static constexpr qreal DEFAULT_MIN_WIDTH_RATIO = 0.3;
    static constexpr qreal DEFAULT_MAX_SPEED = 5.0; // 像素/毫秒
    static constexpr qreal DEFAULT_TRANSITION_LENGTH = 20.0; // 像素
    static constexpr qreal MIN_BASE_WIDTH = 0.5;
    static constexpr qreal MAX_BASE_WIDTH = 50.0;
    static constexpr qreal MIN_WIDTH_RATIO = 0.1;
    static constexpr qreal MAX_WIDTH_RATIO = 1.0;
    static constexpr qreal MIN_MAX_SPEED = 0.5;
    static constexpr qreal MAX_MAX_SPEED = 20.0;
    static constexpr qreal MIN_TRANSITION_LENGTH = 5.0;
    static constexpr qreal MAX_TRANSITION_LENGTH = 100.0;

    // 内部计算方法
    qreal interpolateWidth(qreal speedRatio, const WidthConfig& config) const;
    qreal calculateSpeedRatio(qreal speed, qreal maxSpeed) const;
    qreal applyEasingFunction(qreal ratio) const;
    qreal clampWidth(qreal width, const WidthConfig& config) const;
};

#endif // WIDTHINTERPOLATOR_H