# 设计文档

## 概述

本设计实现了白板自由绘制的速度笔锋功能，通过在现有架构中添加速度感知和动态宽度调整机制，实现根据绘制速度动态调整线条宽度的笔锋效果。设计完全复用现有的 DrawItem、OptimizedDrawingState 和 IncrementalPathBuilder 架构，确保性能和稳定性。

## 架构

### 核心组件关系

```mermaid
graph TD
    A[WhiteBoardWidget] --> B[OptimizedDrawingState]
    B --> C[IncrementalPathBuilder]
    C --> D[SpeedBrushPathBuilder]
    D --> E[SpeedCalculator]
    D --> F[WidthInterpolator]
    B --> G[DrawItem]
    G --> H[SpeedBrushRenderer]
    
    subgraph "速度笔锋核心组件"
        D
        E
        F
        H
    end
    
    subgraph "现有架构"
        A
        B
        C
        G
    end
```

### 数据流

1. **绘制输入** → WhiteBoardWidget → OptimizedDrawingState
2. **路径构建** → IncrementalPathBuilder → SpeedBrushPathBuilder
3. **速度计算** → SpeedCalculator → 速度值
4. **宽度插值** → WidthInterpolator → 动态宽度
5. **路径渲染** → SpeedBrushRenderer → 最终显示

## 组件和接口

### 1. SpeedCalculator（速度计算器）

**职责：** 计算绘制速度并提供平滑的速度值

```cpp
class SpeedCalculator {
public:
    struct SpeedData {
        qreal currentSpeed;      // 当前速度 (像素/毫秒)
        qreal smoothedSpeed;     // 平滑后的速度
        qreal acceleration;      // 加速度
        bool isStationary;       // 是否静止
    };
    
    void addPoint(const QPointF& point, qint64 timestamp);
    SpeedData getCurrentSpeed() const;
    void reset();
    
private:
    static constexpr int SPEED_HISTORY_SIZE = 5;
    static constexpr qreal SMOOTHING_FACTOR = 0.3;
    static constexpr qreal STATIONARY_THRESHOLD = 0.1; // 像素/毫秒
};
```

### 2. WidthInterpolator（宽度插值器）

**职责：** 根据速度计算动态线条宽度

```cpp
class WidthInterpolator {
public:
    struct WidthConfig {
        qreal baseWidth;         // 基础宽度（用户设置的宽度）
        qreal minWidthRatio;     // 最小宽度比例（相对于基础宽度）
        qreal maxSpeed;          // 最大速度阈值
        qreal transitionLength;  // 笔锋过渡长度
    };
    
    qreal calculateWidth(qreal speed, const WidthConfig& config) const;
    qreal calculateTaperLength(qreal speed, const WidthConfig& config) const;
    
private:
    static constexpr qreal DEFAULT_MIN_WIDTH_RATIO = 0.3;
    static constexpr qreal DEFAULT_MAX_SPEED = 5.0; // 像素/毫秒
};
```

### 3. SpeedBrushPathBuilder（速度笔锋路径构建器）

**职责：** 扩展 IncrementalPathBuilder，添加速度感知的路径构建

```cpp
class SpeedBrushPathBuilder {
public:
    struct SpeedBrushPoint {
        QPointF position;
        qreal width;
        qreal speed;
        qint64 timestamp;
    };
    
    void addSpeedBrushPoint(const QPointF& point, qint64 timestamp, qreal baseWidth);
    QPainterPath createVariableWidthPath() const;
    QVector<SpeedBrushPoint> getSpeedBrushPoints() const;
    
private:
    SpeedCalculator m_speedCalculator;
    WidthInterpolator m_widthInterpolator;
    QVector<SpeedBrushPoint> m_speedBrushPoints;
    
    QPainterPath createTaperedSegment(const SpeedBrushPoint& start, 
                                     const SpeedBrushPoint& end) const;
};
```

### 4. SpeedBrushRenderer（速度笔锋渲染器）

**职责：** 渲染变宽度的笔锋路径

```cpp
class SpeedBrushRenderer {
public:
    static void renderSpeedBrushPath(QPainter* painter, 
                                   const QPainterPath& path,
                                   const QVector<SpeedBrushPathBuilder::SpeedBrushPoint>& points,
                                   const QPen& basePen,
                                   const QBrush& brush);
    
private:
    static QPainterPath createVariableWidthStroke(
        const QVector<SpeedBrushPathBuilder::SpeedBrushPoint>& points);
    static void renderWithFeathering(QPainter* painter, 
                                   const QPainterPath& strokePath,
                                   const QPen& pen, 
                                   const QBrush& brush);
};
```

## 数据模型

### SpeedBrushData（速度笔锋数据）

存储在 DrawItem 中的速度笔锋相关数据：

```cpp
struct SpeedBrushData {
    bool enabled = false;                                    // 是否启用速度笔锋
    QVector<SpeedBrushPathBuilder::SpeedBrushPoint> points; // 速度笔锋点数据
    WidthInterpolator::WidthConfig widthConfig;            // 宽度配置
    
    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
    // 内存使用估算
    qint64 memoryUsage() const;
};
```

### DrawItem 扩展

在现有 DrawItem 类中添加速度笔锋支持：

```cpp
class DrawItem : public QGraphicsItem {
private:
    SpeedBrushData m_speedBrushData;  // 速度笔锋数据
    
public:
    // 速度笔锋接口
    void setSpeedBrushData(const SpeedBrushData& data);
    SpeedBrushData getSpeedBrushData() const;
    bool hasSpeedBrush() const;
    
    // 重写绘制方法以支持速度笔锋
    void paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget) override;
};
```

## 错误处理

### 1. 速度计算异常

- **问题：** 时间戳异常或点位数据无效
- **处理：** 使用默认速度值，记录警告日志
- **恢复：** 重置速度计算器状态

### 2. 宽度插值异常

- **问题：** 配置参数无效或计算结果超出范围
- **处理：** 使用基础宽度作为回退值
- **恢复：** 验证并修正配置参数

### 3. 路径构建异常

- **问题：** 内存不足或路径过于复杂
- **处理：** 降级到普通路径构建
- **恢复：** 清理缓存，简化路径

### 4. 渲染异常

- **问题：** 绘制设备异常或路径数据损坏
- **处理：** 使用标准路径渲染作为回退
- **恢复：** 重新构建路径数据

## 测试策略

### 1. 单元测试

- **SpeedCalculator 测试**
  - 速度计算准确性
  - 平滑算法效果
  - 边界条件处理

- **WidthInterpolator 测试**
  - 宽度插值算法
  - 配置参数验证
  - 极值情况处理

- **SpeedBrushPathBuilder 测试**
  - 路径构建正确性
  - 内存使用效率
  - 并发安全性

### 2. 集成测试

- **绘制流程测试**
  - 完整绘制流程验证
  - 性能基准测试
  - 内存泄漏检测

- **序列化测试**
  - 数据完整性验证
  - 版本兼容性测试
  - 错误恢复测试

### 3. 性能测试

- **实时绘制性能**
  - 帧率稳定性测试
  - CPU 使用率监控
  - 内存使用监控

- **复杂场景测试**
  - 大量笔锋路径处理
  - 快速绘制响应性
  - 长时间绘制稳定性

### 4. 用户体验测试

- **视觉效果验证**
  - 笔锋过渡自然性
  - 不同速度下的效果
  - 与其他功能的兼容性

- **交互响应测试**
  - 绘制延迟测试
  - 触摸响应准确性
  - 多点触控兼容性

## 性能优化

### 1. 计算优化

- **速度计算缓存：** 避免重复计算相同的速度值
- **批量处理：** 批量处理多个点的速度计算
- **增量更新：** 只计算变化部分的速度数据

### 2. 内存优化

- **点数据压缩：** 使用紧凑的数据结构存储点信息
- **历史数据清理：** 定期清理不需要的历史速度数据
- **延迟分配：** 只在需要时分配速度笔锋相关内存

### 3. 渲染优化

- **路径简化：** 简化复杂的变宽度路径
- **分层渲染：** 将笔锋效果分层渲染以提高效率
- **缓存机制：** 缓存渲染结果避免重复计算

### 4. 并发优化

- **异步计算：** 将复杂的速度计算移到后台线程
- **锁优化：** 使用细粒度锁减少竞争
- **无锁算法：** 在可能的情况下使用无锁数据结构

## 兼容性考虑

### 1. 向后兼容

- **数据格式：** 新的速度笔锋数据作为可选字段添加
- **渲染回退：** 不支持速度笔锋时自动回退到标准渲染
- **版本标识：** 在序列化数据中添加版本标识

### 2. 平台兼容

- **时间精度：** 处理不同平台的时间戳精度差异
- **浮点计算：** 确保浮点计算在不同平台上的一致性
- **内存对齐：** 考虑不同平台的内存对齐要求

### 3. 设备兼容

- **触摸设备：** 优化触摸设备的速度检测
- **鼠标设备：** 适配鼠标设备的绘制特性
- **高DPI设备：** 处理高DPI设备的坐标和速度计算