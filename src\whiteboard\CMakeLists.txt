# WhiteBoard模块CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Concurrent Svg OpenGL OpenGLWidgets)

# 收集所有源文件
file(GLOB_RECURSE WHITEBOARD_SOURCES
    "core/*.cpp"
    "core/*.h"
    "tools/*.cpp"
    "tools/*.h"
    "graphics/*.cpp"
    "graphics/*.h"
    "ui/*.cpp"
    "ui/*.h"
    "commands/*.cpp"
    "commands/*.h"
    "serialization/*.cpp"
    "serialization/*.h"
    "export/*.cpp"
    "export/*.h"
    "utils/*.cpp"
    "utils/*.h"
    "optimization/*.cpp"
    "optimization/*.h"
    "rendering/*.cpp"
    "rendering/*.h"
    "constants/*.cpp"
    "constants/*.h"
    "data/*.cpp"
    "data/*.h"
)

# 定义资源文件
set(WHITEBOARD_RESOURCES
    "resources/whiteboard_resources.qrc"
)

# 处理Qt资源文件
qt_add_resources(WHITEBOARD_QRC_SOURCES ${WHITEBOARD_RESOURCES})

# 创建whiteboard库
add_library(whiteboard STATIC ${WHITEBOARD_SOURCES} ${WHITEBOARD_QRC_SOURCES})

# 设置包含目录
target_include_directories(whiteboard PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/core
    ${CMAKE_CURRENT_SOURCE_DIR}/tools
    ${CMAKE_CURRENT_SOURCE_DIR}/graphics
    ${CMAKE_CURRENT_SOURCE_DIR}/ui
    ${CMAKE_CURRENT_SOURCE_DIR}/commands
    ${CMAKE_CURRENT_SOURCE_DIR}/serialization
    ${CMAKE_CURRENT_SOURCE_DIR}/undo
    ${CMAKE_CURRENT_SOURCE_DIR}/sync
    ${CMAKE_CURRENT_SOURCE_DIR}/test
)

# 链接Qt6库
target_link_libraries(whiteboard PUBLIC
    Qt6::Core
    Qt6::Widgets
    Qt6::Concurrent
    Qt6::Svg
    Qt6::OpenGL
    Qt6::OpenGLWidgets
)

# 设置编译器特定选项
if(MSVC)
    target_compile_options(whiteboard PRIVATE /W4)
else()
    target_compile_options(whiteboard PRIVATE -Wall -Wextra -Wpedantic)
endif()

