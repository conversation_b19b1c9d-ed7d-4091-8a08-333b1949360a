#include "SpeedBrushData.h"
#include <QJsonArray>
#include <QDebug>

void SpeedBrushData::addPoint(const IncrementalPathBuilder::SpeedBrushPoint& point)
{
    points.append(point);
}

void SpeedBrushData::clear()
{
    points.clear();
    enabled = false;
}

QJsonObject SpeedBrushData::toJson() const
{
    QJsonObject json;
    
    // 基本属性
    json["enabled"] = enabled;
    
    // 宽度配置
    json["widthConfig"] = widthConfig.toJson();
    
    // 速度笔锋点数据
    QJsonArray pointsArray;
    for (const auto& point : points) {
        QJsonObject pointObj;
        pointObj["x"] = point.position.x();
        pointObj["y"] = point.position.y();
        pointObj["width"] = point.width;
        pointObj["speed"] = point.speed;
        pointObj["timestamp"] = point.timestamp;
        pointsArray.append(pointObj);
    }
    json["points"] = pointsArray;
    
    return json;
}

void SpeedBrushData::fromJson(const QJsonObject& json)
{
    // 基本属性
    enabled = json["enabled"].toBool(false);
    
    // 宽度配置
    if (json.contains("widthConfig") && json["widthConfig"].isObject()) {
        widthConfig.fromJson(json["widthConfig"].toObject());
    }
    
    // 速度笔锋点数据
    points.clear();
    if (json.contains("points") && json["points"].isArray()) {
        QJsonArray pointsArray = json["points"].toArray();
        points.reserve(pointsArray.size());
        
        for (const auto& value : pointsArray) {
            if (value.isObject()) {
                QJsonObject pointObj = value.toObject();
                
                IncrementalPathBuilder::SpeedBrushPoint point;
                point.position.setX(pointObj["x"].toDouble(0.0));
                point.position.setY(pointObj["y"].toDouble(0.0));
                point.width = pointObj["width"].toDouble(1.0);
                point.speed = pointObj["speed"].toDouble(0.0);
                point.timestamp = pointObj["timestamp"].toVariant().toLongLong();
                
                points.append(point);
            }
        }
    }
    
    // 验证和规范化数据
    normalize();
}

qint64 SpeedBrushData::memoryUsage() const
{
    qint64 usage = sizeof(SpeedBrushData);
    
    // 计算点数据的内存使用
    usage += points.size() * sizeof(IncrementalPathBuilder::SpeedBrushPoint);
    
    // 考虑QVector的容量开销
    usage += (points.capacity() - points.size()) * sizeof(IncrementalPathBuilder::SpeedBrushPoint);
    
    return usage;
}

bool SpeedBrushData::isValid() const
{
    // 检查基本状态
    if (!enabled && points.isEmpty()) {
        return true; // 未启用且无数据是有效状态
    }
    
    if (enabled && points.isEmpty()) {
        return false; // 启用但无数据是无效状态
    }
    
    // 验证宽度配置
    if (!validateWidthConfig()) {
        return false;
    }
    
    // 验证点数据
    if (!validatePoints()) {
        return false;
    }
    
    return true;
}

void SpeedBrushData::normalize()
{
    // 规范化宽度配置
    widthConfig.normalize();
    
    // 如果启用但没有点数据，禁用功能
    if (enabled && points.isEmpty()) {
        enabled = false;
        qWarning() << "SpeedBrushData::normalize: 启用速度笔锋但无点数据，自动禁用";
    }
    
    // 如果有点数据但未启用，自动启用
    if (!enabled && !points.isEmpty()) {
        enabled = true;
        qWarning() << "SpeedBrushData::normalize: 存在速度笔锋数据但未启用，自动启用";
    }
    
    // 验证和修正点数据
    for (auto& point : points) {
        // 确保宽度为正值
        if (point.width <= 0.0) {
            point.width = widthConfig.baseWidth;
            qWarning() << "SpeedBrushData::normalize: 修正无效的点宽度";
        }
        
        // 确保速度为非负值
        if (point.speed < 0.0) {
            point.speed = 0.0;
            qWarning() << "SpeedBrushData::normalize: 修正负速度值";
        }
        
        // 确保时间戳为正值
        if (point.timestamp <= 0) {
            point.timestamp = 1; // 设置为最小有效时间戳
            qWarning() << "SpeedBrushData::normalize: 修正无效的时间戳";
        }
    }
}

bool SpeedBrushData::validatePoints() const
{
    for (const auto& point : points) {
        // 检查点位置是否有效
        if (!qIsFinite(point.position.x()) || !qIsFinite(point.position.y())) {
            return false;
        }
        
        // 检查宽度是否有效
        if (point.width <= 0.0 || !qIsFinite(point.width)) {
            return false;
        }
        
        // 检查速度是否有效
        if (point.speed < 0.0 || !qIsFinite(point.speed)) {
            return false;
        }
        
        // 检查时间戳是否有效
        if (point.timestamp <= 0) {
            return false;
        }
    }
    
    return true;
}

bool SpeedBrushData::validateWidthConfig() const
{
    return widthConfig.isValid();
}