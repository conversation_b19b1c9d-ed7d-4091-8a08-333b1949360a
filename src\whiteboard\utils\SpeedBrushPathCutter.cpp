#include "SpeedBrushPathCutter.h"
#include <QDebug>
#include <QtMath>

SpeedBrushPathCutter::CutResult SpeedBrushPathCutter::cutSpeedBrushPath(
    const SpeedBrushData& speedBrushData,
    const QPainterPath& eraserPath,
    qreal eraserWidth)
{
    CutResult result;
    
    if (!speedBrushData.enabled || speedBrushData.isEmpty() || eraserPath.isEmpty()) {
        // 如果没有速度笔锋数据或橡皮擦路径为空，返回原始数据
        result.segments.append(speedBrushData);
        return result;
    }
    
    const auto& points = speedBrushData.getPoints();
    if (points.isEmpty()) {
        result.segments.append(speedBrushData);
        return result;
    }
    
    // 找到所有需要切割的点
    QVector<int> cutIndices;
    QVector<bool> erasedPoints(points.size(), false);
    
    for (int i = 0; i < points.size(); ++i) {
        const auto& point = points[i];
        if (isPointErased(point.position, point.width, eraserPath, eraserWidth)) {
            erasedPoints[i] = true;
            result.wasModified = true;
        }
    }
    
    if (!result.wasModified) {
        // 没有点被擦除，返回原始数据
        result.segments.append(speedBrushData);
        return result;
    }
    
    // 构建连续的未被擦除的路径段
    QVector<IncrementalPathBuilder::SpeedBrushPoint> currentSegment;
    
    for (int i = 0; i < points.size(); ++i) {
        if (!erasedPoints[i]) {
            // 点未被擦除，添加到当前段
            currentSegment.append(points[i]);
        } else {
            // 点被擦除，结束当前段
            if (!currentSegment.isEmpty()) {
                // 创建新的速度笔锋数据段
                SpeedBrushData segmentData = recalculateSpeedBrushData(currentSegment, speedBrushData.getWidthConfig());
                if (!segmentData.isEmpty()) {
                    result.segments.append(segmentData);
                }
                currentSegment.clear();
            }
        }
    }
    
    // 处理最后一段
    if (!currentSegment.isEmpty()) {
        SpeedBrushData segmentData = recalculateSpeedBrushData(currentSegment, speedBrushData.getWidthConfig());
        if (!segmentData.isEmpty()) {
            result.segments.append(segmentData);
        }
    }
    
    qDebug() << "SpeedBrushPathCutter: 切割完成，原始点数:" << points.size() 
             << "生成段数:" << result.segments.size();
    
    return result;
}

bool SpeedBrushPathCutter::isPointErased(const QPointF& point, qreal pointWidth,
                                        const QPainterPath& eraserPath, qreal eraserWidth)
{
    if (eraserPath.isEmpty()) {
        return false;
    }
    
    // 检查点是否在橡皮擦路径内
    if (eraserPath.contains(point)) {
        return true;
    }
    
    // 考虑点的宽度和橡皮擦宽度，检查是否有重叠
    qreal totalRadius = (pointWidth + eraserWidth) / 2.0;
    
    // 检查点到橡皮擦路径的最短距离
    QRectF eraserBounds = eraserPath.boundingRect();
    QRectF pointBounds(point.x() - totalRadius, point.y() - totalRadius,
                      totalRadius * 2, totalRadius * 2);
    
    if (!eraserBounds.intersects(pointBounds)) {
        return false;
    }
    
    // 更精确的检查：创建点的圆形区域，检查是否与橡皮擦路径相交
    QPainterPath pointPath;
    pointPath.addEllipse(point, pointWidth / 2.0, pointWidth / 2.0);
    
    // 创建扩展的橡皮擦路径
    QPainterPathStroker stroker;
    stroker.setWidth(eraserWidth);
    stroker.setCapStyle(Qt::RoundCap);
    stroker.setJoinStyle(Qt::RoundJoin);
    QPainterPath expandedEraserPath = stroker.createStroke(eraserPath);
    
    return expandedEraserPath.intersects(pointPath);
}

QVector<SpeedBrushData> SpeedBrushPathCutter::splitSpeedBrushData(
    const SpeedBrushData& speedBrushData, int splitIndex)
{
    QVector<SpeedBrushData> result;
    
    if (!speedBrushData.enabled || speedBrushData.isEmpty() || splitIndex <= 0) {
        result.append(speedBrushData);
        return result;
    }
    
    const auto& points = speedBrushData.getPoints();
    if (splitIndex >= points.size()) {
        result.append(speedBrushData);
        return result;
    }
    
    // 创建第一段
    QVector<IncrementalPathBuilder::SpeedBrushPoint> firstSegment;
    for (int i = 0; i < splitIndex; ++i) {
        firstSegment.append(points[i]);
    }
    
    // 创建第二段
    QVector<IncrementalPathBuilder::SpeedBrushPoint> secondSegment;
    for (int i = splitIndex; i < points.size(); ++i) {
        secondSegment.append(points[i]);
    }
    
    // 重新计算两段的速度笔锋数据
    if (!firstSegment.isEmpty()) {
        SpeedBrushData firstData = recalculateSpeedBrushData(firstSegment, speedBrushData.getWidthConfig());
        if (!firstData.isEmpty()) {
            result.append(firstData);
        }
    }
    
    if (!secondSegment.isEmpty()) {
        SpeedBrushData secondData = recalculateSpeedBrushData(secondSegment, speedBrushData.getWidthConfig());
        if (!secondData.isEmpty()) {
            result.append(secondData);
        }
    }
    
    return result;
}

SpeedBrushData SpeedBrushPathCutter::recalculateSpeedBrushData(
    const QVector<IncrementalPathBuilder::SpeedBrushPoint>& points,
    const WidthInterpolator::WidthConfig& originalConfig)
{
    SpeedBrushData result;
    
    if (points.isEmpty()) {
        return result;
    }
    
    // 启用速度笔锋并设置配置
    result.enabled = true;
    result.setWidthConfig(originalConfig);
    
    // 重新计算路径点的速度和宽度
    QVector<IncrementalPathBuilder::SpeedBrushPoint> recalculatedPoints;
    
    for (int i = 0; i < points.size(); ++i) {
        SpeedBrushData::SpeedBrushPoint newPoint = points[i];
        
        // 重新计算速度（基于相邻点）
        if (i > 0 && i < points.size() - 1) {
            const auto& prevPoint = points[i - 1];
            const auto& nextPoint = points[i + 1];
            
            qreal dist1 = distance(prevPoint.position, newPoint.position);
            qreal dist2 = distance(newPoint.position, nextPoint.position);
            qreal time1 = qAbs(newPoint.timestamp - prevPoint.timestamp);
            qreal time2 = qAbs(nextPoint.timestamp - newPoint.timestamp);
            
            if (time1 > 0 && time2 > 0) {
                qreal speed1 = dist1 / time1;
                qreal speed2 = dist2 / time2;
                newPoint.speed = (speed1 + speed2) / 2.0;  // 平均速度
            }
        }
        
        // 根据重新计算的速度调整宽度
        qreal speedRatio = qMin(newPoint.speed / originalConfig.maxSpeed, 1.0);
        qreal widthRatio = originalConfig.minWidthRatio + 
                          (1.0 - originalConfig.minWidthRatio) * (1.0 - speedRatio);
        newPoint.width = originalConfig.baseWidth * widthRatio;
        
        recalculatedPoints.append(newPoint);
    }
    
    result.setPoints(recalculatedPoints);
    
    qDebug() << "SpeedBrushPathCutter: 重新计算速度笔锋数据，点数:" << recalculatedPoints.size();
    
    return result;
}

qreal SpeedBrushPathCutter::distance(const QPointF& p1, const QPointF& p2)
{
    qreal dx = p2.x() - p1.x();
    qreal dy = p2.y() - p1.y();
    return qSqrt(dx * dx + dy * dy);
}

bool SpeedBrushPathCutter::segmentIntersectsEraser(const QPointF& start, const QPointF& end,
                                                  qreal startWidth, qreal endWidth,
                                                  const QPainterPath& eraserPath, qreal eraserWidth)
{
    if (eraserPath.isEmpty()) {
        return false;
    }
    
    // 检查线段端点
    if (isPointErased(start, startWidth, eraserPath, eraserWidth) ||
        isPointErased(end, endWidth, eraserPath, eraserWidth)) {
        return true;
    }
    
    // 检查线段中点（简化的相交检测）
    QPointF midPoint = (start + end) / 2.0;
    qreal midWidth = (startWidth + endWidth) / 2.0;
    
    return isPointErased(midPoint, midWidth, eraserPath, eraserWidth);
}