#include "SpeedCalculator.h"
#include <QtMath>
#include <QDebug>
#include <algorithm>

SpeedCalculator::SpeedCalculator()
    : m_maxHistorySize(DEFAULT_HISTORY_SIZE)
    , m_smoothingFactor(DEFAULT_SMOOTHING_FACTOR)
    , m_stationaryThreshold(DEFAULT_STATIONARY_THRESHOLD)
{
    m_history.reserve(m_maxHistorySize + 1); // 预留空间避免频繁重分配
}

void SpeedCalculator::addPoint(const QPointF& point, qint64 timestamp)
{
    // 验证输入参数
    if (timestamp < 0) {
        qWarning() << "SpeedCalculator: Invalid timestamp" << timestamp;
        return;
    }

    // 检查时间戳是否合理（不能倒退太多）
    if (!m_history.isEmpty()) {
        qint64 lastTimestamp = m_history.last().timestamp;
        if (timestamp < lastTimestamp - 1000) { // 允许1秒的时间倒退容错
            qWarning() << "SpeedCalculator: Timestamp went backwards significantly" 
                      << "last:" << lastTimestamp << "current:" << timestamp;
            // 重置历史记录以避免错误的速度计算
            reset();
        }
    }

    // 添加到历史记录
    addToHistory(point, timestamp);
    
    // 修剪历史记录
    trimHistory();
    
    // 标记缓存无效
    m_speedDataValid = false;
}

SpeedCalculator::SpeedData SpeedCalculator::getCurrentSpeed() const
{
    if (!m_speedDataValid) {
        updateSpeedCache();
    }
    return m_cachedSpeedData;
}

void SpeedCalculator::reset()
{
    m_history.clear();
    m_speedDataValid = false;
    m_cachedSpeedData = SpeedData();
}

void SpeedCalculator::setSmoothingFactor(qreal factor)
{
    if (factor < 0.0 || factor > 1.0) {
        qWarning() << "SpeedCalculator: Invalid smoothing factor" << factor << "using default";
        m_smoothingFactor = DEFAULT_SMOOTHING_FACTOR;
    } else {
        m_smoothingFactor = factor;
        m_speedDataValid = false; // 重新计算
    }
}

void SpeedCalculator::setStationaryThreshold(qreal threshold)
{
    if (threshold < 0.0) {
        qWarning() << "SpeedCalculator: Invalid stationary threshold" << threshold << "using default";
        m_stationaryThreshold = DEFAULT_STATIONARY_THRESHOLD;
    } else {
        m_stationaryThreshold = threshold;
        m_speedDataValid = false; // 重新计算
    }
}

void SpeedCalculator::setHistorySize(int size)
{
    if (size < 2) {
        qWarning() << "SpeedCalculator: Invalid history size" << size << "using default";
        m_maxHistorySize = DEFAULT_HISTORY_SIZE;
    } else {
        m_maxHistorySize = size;
        m_history.reserve(m_maxHistorySize + 1);
        trimHistory(); // 立即修剪
        m_speedDataValid = false; // 重新计算
    }
}

bool SpeedCalculator::hasValidSpeed() const
{
    return m_history.size() >= 2;
}

qreal SpeedCalculator::getAverageSpeed() const
{
    if (m_history.size() < 2) {
        return 0.0;
    }

    qreal totalSpeed = 0.0;
    int validSpeeds = 0;

    for (int i = 1; i < m_history.size(); ++i) {
        qreal speed = calculateInstantaneousSpeed(m_history[i], m_history[i-1]);
        if (speed >= 0.0) { // 只计算有效速度
            totalSpeed += speed;
            validSpeeds++;
        }
    }

    return validSpeeds > 0 ? totalSpeed / validSpeeds : 0.0;
}

qreal SpeedCalculator::getMaxSpeed() const
{
    if (m_history.size() < 2) {
        return 0.0;
    }

    qreal maxSpeed = 0.0;
    for (int i = 1; i < m_history.size(); ++i) {
        qreal speed = calculateInstantaneousSpeed(m_history[i], m_history[i-1]);
        if (speed > maxSpeed) {
            maxSpeed = speed;
        }
    }

    return maxSpeed;
}

qreal SpeedCalculator::calculateInstantaneousSpeed(const HistoryPoint& current, const HistoryPoint& previous) const
{
    qint64 timeDelta = current.timestamp - previous.timestamp;
    
    // 避免除零和处理时间倒退
    if (timeDelta <= 0) {
        return 0.0;
    }

    // 如果时间间隔太小，可能是噪声，返回0
    if (timeDelta < MIN_TIME_DELTA) {
        return 0.0;
    }

    // 计算距离
    QPointF deltaPos = current.position - previous.position;
    qreal distance = qSqrt(deltaPos.x() * deltaPos.x() + deltaPos.y() * deltaPos.y());

    // 计算速度 (像素/毫秒)
    return distance / timeDelta;
}

qreal SpeedCalculator::calculateSmoothedSpeed(qreal currentSpeed) const
{
    if (m_history.size() < 2) {
        return currentSpeed;
    }

    // 获取上一个点的平滑速度
    qreal previousSmoothedSpeed = 0.0;
    if (m_history.size() >= 2) {
        // 查找最近的有效平滑速度
        for (int i = m_history.size() - 2; i >= 0; --i) {
            if (m_history[i].speed > 0.0) {
                previousSmoothedSpeed = m_history[i].speed;
                break;
            }
        }
    }

    // 指数移动平均平滑
    return m_smoothingFactor * currentSpeed + (1.0 - m_smoothingFactor) * previousSmoothedSpeed;
}

qreal SpeedCalculator::calculateAcceleration() const
{
    if (m_history.size() < 3) {
        return 0.0;
    }

    // 计算最近两个速度值的加速度
    const HistoryPoint& current = m_history.last();
    const HistoryPoint& previous = m_history[m_history.size() - 2];
    const HistoryPoint& beforePrevious = m_history[m_history.size() - 3];

    qreal currentSpeed = calculateInstantaneousSpeed(current, previous);
    qreal previousSpeed = calculateInstantaneousSpeed(previous, beforePrevious);

    qint64 timeDelta = current.timestamp - previous.timestamp;
    if (timeDelta <= 0) {
        return 0.0;
    }

    // 加速度 = 速度变化 / 时间变化
    return (currentSpeed - previousSpeed) / timeDelta;
}

bool SpeedCalculator::isCurrentlyStationary(qreal speed) const
{
    return speed < m_stationaryThreshold;
}

void SpeedCalculator::updateSpeedCache() const
{
    if (m_history.size() < 2) {
        m_cachedSpeedData = SpeedData();
        m_speedDataValid = true;
        return;
    }

    // 计算当前瞬时速度
    const HistoryPoint& current = m_history.last();
    const HistoryPoint& previous = m_history[m_history.size() - 2];
    qreal currentSpeed = calculateInstantaneousSpeed(current, previous);

    // 计算平滑速度
    qreal smoothedSpeed = calculateSmoothedSpeed(currentSpeed);

    // 计算加速度
    qreal acceleration = calculateAcceleration();

    // 判断是否静止
    bool isStationary = isCurrentlyStationary(smoothedSpeed);

    // 更新缓存
    m_cachedSpeedData = SpeedData(currentSpeed, smoothedSpeed, acceleration, isStationary);
    m_speedDataValid = true;

    // 更新历史记录中的速度值（用于下次平滑计算）
    const_cast<SpeedCalculator*>(this)->m_history.last().speed = smoothedSpeed;
}

void SpeedCalculator::addToHistory(const QPointF& point, qint64 timestamp)
{
    m_history.append(HistoryPoint(point, timestamp));
}

void SpeedCalculator::trimHistory()
{
    while (m_history.size() > m_maxHistorySize) {
        m_history.removeFirst();
    }
}